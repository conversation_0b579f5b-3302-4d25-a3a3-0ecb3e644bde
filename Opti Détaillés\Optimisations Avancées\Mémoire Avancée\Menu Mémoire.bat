@echo off
chcp 65001
set "base_path=%~dp0..\..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations Mémoire Avancée

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███╗   ███╗███████╗███╗   ███╗ ██████╗ ██████╗ ██╗   ██╗
echo       ████╗ ████║██╔════╝████╗ ████║██╔═══██╗██╔══██╗╚██╗ ██╔╝
echo       ██╔████╔██║█████╗  ██╔████╔██║██║   ██║██████╔╝ ╚████╔╝ 
echo       ██║╚██╔╝██║██╔══╝  ██║╚██╔╝██║██║   ██║██╔══██╗  ╚██╔╝  
echo       ██║ ╚═╝ ██║███████╗██║ ╚═╝ ██║╚██████╔╝██║  ██║   ██║   
echo       ╚═╝     ╚═╝╚══════╝╚═╝     ╚═╝ ╚═════╝ ╚═╝  ╚═╝   ╚═╝   
echo.
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        OPTIMISATIONS MÉMOIRE AVANCÉE                        ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. 🧠 Memory Compression            │  6. 📊 Analyse Mémoire              ║
echo ║  2. 💾 Paging File Optimization      │  7. 🎯 Optimisation Complète        ║
echo ║  3. ⚡ Memory Standby List           │  8. 🔄 Restaurer Mémoire            ║
echo ║  4. 🚀 Large Page Support            │  9. 🧹 Nettoyage Mémoire            ║
echo ║  5. 🔧 Memory Management Advanced    │  0. 🚪 Retour Menu Principal        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890 /M "Sélectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO NettoyageMemoire
IF ERRORLEVEL 8 GOTO RestaurerMemoire
IF ERRORLEVEL 7 GOTO OptimisationCompleteMemoire
IF ERRORLEVEL 6 GOTO AnalyseMemoire
IF ERRORLEVEL 5 GOTO MemoryManagementAdvanced
IF ERRORLEVEL 4 GOTO LargePageSupport
IF ERRORLEVEL 3 GOTO MemoryStandbyList
IF ERRORLEVEL 2 GOTO PagingFileOptimization
IF ERRORLEVEL 1 GOTO MemoryCompression

:MemoryCompression
CLS
title Memory Compression Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        MEMORY COMPRESSION OPTIMIZATION                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser la compression mémoire Windows
echo • Réduire l'utilisation de la mémoire physique
echo • Améliorer les performances système
echo • Configurer les paramètres de compression avancés
echo.
choice /c:yn /n /m "Optimiser la compression mémoire? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyMemoryCompression
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyMemoryCompression
echo.
echo Optimisation de la compression mémoire...
regedit /s "%base_path%Ressources\Mémoire\Memory Compression Optimization.reg"
powershell -Command "Enable-MMAgent -MemoryCompression"
powershell -Command "Set-MMAgent -MemoryCompression $true"
echo.
echo ✅ Compression mémoire optimisée avec succès!
echo.
echo Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:PagingFileOptimization
CLS
title Paging File Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                         PAGING FILE OPTIMIZATION                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Choisissez votre configuration de fichier d'échange:
echo.
echo 1. 🚀 Configuration Gaming (Taille fixe optimisée)
echo 2. 💻 Configuration Équilibrée (Taille système)
echo 3. 🔧 Configuration Personnalisée
echo 4. ❌ Désactiver le fichier d'échange
echo 5. 🚪 Retour au menu
echo.

CHOICE /C 12345 /M "Sélectionnez votre configuration:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO DisablePagingFile
IF ERRORLEVEL 3 GOTO CustomPagingFile
IF ERRORLEVEL 2 GOTO BalancedPagingFile
IF ERRORLEVEL 1 GOTO GamingPagingFile

:GamingPagingFile
echo.
echo Configuration du fichier d'échange pour gaming...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Set Gaming Paging File.ps1"
echo ✅ Fichier d'échange gaming configuré!
GOTO PagingComplete

:BalancedPagingFile
echo.
echo Configuration du fichier d'échange équilibré...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Set Balanced Paging File.ps1"
echo ✅ Fichier d'échange équilibré configuré!
GOTO PagingComplete

:CustomPagingFile
echo.
set /p "minSize=Taille minimale en MB (recommandé: 1024): "
set /p "maxSize=Taille maximale en MB (recommandé: 4096): "
echo.
echo Configuration du fichier d'échange personnalisé...
powershell -Command "$cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $false; $cs.Put(); $pf = Get-WmiObject -Class Win32_PageFileSetting; if($pf) { $pf.Delete() }; Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{name='C:\pagefile.sys'; InitialSize=%minSize%; MaximumSize=%maxSize%}"
echo ✅ Fichier d'échange personnalisé configuré!
GOTO PagingComplete

:DisablePagingFile
echo.
echo ⚠️  ATTENTION: Désactiver le fichier d'échange peut causer des problèmes
echo si vous n'avez pas suffisamment de RAM (16GB+ recommandés).
echo.
choice /c:yn /n /m "Êtes-vous sûr de vouloir désactiver le fichier d'échange? [Y]es/[N]o"
if %ERRORLEVEL% == 2 GOTO Menu

:MemoryStandbyList
CLS
title Memory Standby List Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                      MEMORY STANDBY LIST OPTIMIZATION                       ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser la gestion de la liste de veille mémoire
echo • Libérer automatiquement la mémoire inutilisée
echo • Améliorer la réactivité du système
echo • Configurer le nettoyage automatique de la mémoire
echo.
choice /c:yn /n /m "Optimiser la liste de veille mémoire? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyMemoryStandby
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyMemoryStandby
echo.
echo Optimisation de la liste de veille mémoire...
regedit /s "%base_path%Ressources\Mémoire\Memory Standby List Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Configure Memory Standby.ps1"
echo.
echo ✅ Liste de veille mémoire optimisée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:LargePageSupport
CLS
title Large Page Support

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            LARGE PAGE SUPPORT                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Activer le support des grandes pages (Large Pages)
echo • Améliorer les performances des applications compatibles
echo • Optimiser l'utilisation de la mémoire pour les jeux
echo • Configurer les privilèges nécessaires
echo.
choice /c:yn /n /m "Activer le support des grandes pages? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyLargePages
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyLargePages
echo.
echo Activation du support des grandes pages...
regedit /s "%base_path%Ressources\Mémoire\Large Page Support.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Enable Large Pages.ps1"
echo.
echo ✅ Support des grandes pages activé avec succès!
echo.
echo ⚠️  Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:MemoryManagementAdvanced
CLS
title Memory Management Advanced

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       MEMORY MANAGEMENT ADVANCED                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser la gestion avancée de la mémoire
echo • Configurer les paramètres de cache système
echo • Améliorer l'allocation mémoire
echo • Optimiser les performances des applications
echo.
choice /c:yn /n /m "Appliquer la gestion mémoire avancée? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyAdvancedMemory
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyAdvancedMemory
echo.
echo Application de la gestion mémoire avancée...
regedit /s "%base_path%Ressources\Mémoire\Advanced Memory Management.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Configure Advanced Memory.ps1"
echo.
echo ✅ Gestion mémoire avancée appliquée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:AnalyseMemoire
CLS
title Analyse Mémoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              ANALYSE MÉMOIRE                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Analyse de l'utilisation mémoire actuelle...
echo.
powershell -Command "Get-WmiObject -Class Win32_OperatingSystem | Select-Object @{Name='RAM Totale (GB)';Expression={[math]::Round($_.TotalVisibleMemorySize/1MB,2)}}, @{Name='RAM Libre (GB)';Expression={[math]::Round($_.FreePhysicalMemory/1MB,2)}}, @{Name='RAM Utilisée (GB)';Expression={[math]::Round(($_.TotalVisibleMemorySize-$_.FreePhysicalMemory)/1MB,2)}}, @{Name='Utilisation (%)';Expression={[math]::Round((($_.TotalVisibleMemorySize-$_.FreePhysicalMemory)/$_.TotalVisibleMemorySize)*100,1)}} | Format-List"
echo.
echo Informations sur le fichier d'échange:
powershell -Command "Get-WmiObject -Class Win32_PageFileUsage | Select-Object Name, @{Name='Taille Allouée (MB)';Expression={$_.AllocatedBaseSize}}, @{Name='Taille Actuelle (MB)';Expression={$_.CurrentUsage}}, @{Name='Taille Max (MB)';Expression={$_.PeakUsage}} | Format-Table -AutoSize"
echo.
echo Processus utilisant le plus de mémoire:
powershell -Command "Get-Process | Sort-Object WorkingSet -Descending | Select-Object -First 10 Name, @{Name='Mémoire (MB)';Expression={[math]::Round($_.WorkingSet/1MB,2)}} | Format-Table -AutoSize"
echo.
echo Appuyez sur une touche pour revenir au menu...
pause >nul
GOTO Menu

:OptimisationCompleteMemoire
CLS
title Optimisation Complète Mémoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        OPTIMISATION COMPLÈTE MÉMOIRE                        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option appliquera TOUTES les optimisations mémoire:
echo • Memory Compression Optimization
echo • Paging File Gaming Configuration
echo • Memory Standby List Optimization
echo • Large Page Support
echo • Advanced Memory Management
echo.
choice /c:yn /n /m "Appliquer toutes les optimisations mémoire? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyAllMemory
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyAllMemory
echo.
echo Application de toutes les optimisations mémoire...
echo.
echo [1/5] Memory Compression Optimization...
regedit /s "%base_path%Ressources\Mémoire\Memory Compression Optimization.reg"
powershell -Command "Enable-MMAgent -MemoryCompression" 2>nul
powershell -Command "Set-MMAgent -MemoryCompression $true" 2>nul
echo.
echo [2/5] Paging File Gaming Configuration...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Set Gaming Paging File.ps1"
echo.
echo [3/5] Memory Standby List Optimization...
regedit /s "%base_path%Ressources\Mémoire\Memory Standby List Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Configure Memory Standby.ps1"
echo.
echo [4/5] Large Page Support...
regedit /s "%base_path%Ressources\Mémoire\Large Page Support.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Enable Large Pages.ps1"
echo.
echo [5/5] Advanced Memory Management...
regedit /s "%base_path%Ressources\Mémoire\Advanced Memory Management.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Configure Advanced Memory.ps1"
echo.
echo ✅ TOUTES les optimisations mémoire ont été appliquées avec succès!
echo.
echo ⚠️  Redémarrage OBLIGATOIRE pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:RestaurerMemoire
CLS
title Restauration Mémoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            RESTAURATION MÉMOIRE                             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option restaurera tous les paramètres mémoire par défaut:
echo • Compression mémoire par défaut
echo • Fichier d'échange géré par le système
echo • Liste de veille mémoire par défaut
echo • Désactivation Large Page Support
echo • Gestion mémoire Windows par défaut
echo.
choice /c:yn /n /m "Restaurer tous les paramètres mémoire? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO RestoreAllMemory
if %ERRORLEVEL% == 2 GOTO Menu

:RestoreAllMemory
echo.
echo Restauration de tous les paramètres mémoire...
echo.
echo [1/5] Restauration Memory Compression...
regedit /s "%base_path%Ressources\Mémoire\Restore Memory Compression.reg"
powershell -Command "Set-MMAgent -MemoryCompression $false" 2>nul
echo.
echo [2/5] Restauration Paging File...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Restore Default Paging File.ps1"
echo.
echo [3/5] Restauration Memory Standby List...
regedit /s "%base_path%Ressources\Mémoire\Restore Memory Standby.reg"
echo.
echo [4/5] Désactivation Large Page Support...
regedit /s "%base_path%Ressources\Mémoire\Disable Large Pages.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Disable Large Pages.ps1"
echo.
echo [5/5] Restauration Advanced Memory Management...
regedit /s "%base_path%Ressources\Mémoire\Restore Advanced Memory.reg"
echo.
echo ✅ TOUS les paramètres mémoire ont été restaurés avec succès!
echo.
echo ⚠️  Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:NettoyageMemoire
CLS
title Nettoyage Mémoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                             NETTOYAGE MÉMOIRE                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Nettoyage de la mémoire en cours...
echo.
echo • Vidage de la liste de veille mémoire...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()"
echo.
echo • Nettoyage du cache système...
powershell -Command "Clear-RecycleBin -Force -ErrorAction SilentlyContinue"
echo.
echo • Libération de la mémoire inutilisée...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Memory Cleanup.ps1"
echo.
echo ✅ Nettoyage mémoire terminé!
echo.
echo Mémoire libérée avec succès.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:Retour
exit
echo.
echo Désactivation du fichier d'échange...
powershell -Command "$cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $false; $cs.Put(); $pf = Get-WmiObject -Class Win32_PageFileSetting; if($pf) { $pf.Delete() }"
echo ✅ Fichier d'échange désactivé!
GOTO PagingComplete

:PagingComplete
echo.
echo ⚠️  Redémarrage OBLIGATOIRE pour appliquer les changements du fichier d'échange.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu
