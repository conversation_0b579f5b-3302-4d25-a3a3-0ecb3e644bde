@echo off
chcp 65001
echo 🔄 Mise à jour du branding MathysM vers MZEER...
echo.

rem Remplacer dans tous les fichiers .bat
echo 📁 Mise à jour des fichiers .bat...
powershell -Command "(Get-Content 'Opti Détaillés\Optimisations Avancées\Réseau & Latence\Menu Réseau.bat') -replace 'MathysM', 'MZEER' -replace 'beacons\.ai/mathysm', 'https://www.twitch.tv/mzeer_' | Set-Content 'Opti Détaillés\Optimisations Avancées\Réseau & Latence\Menu Réseau.bat'"

powershell -Command "(Get-Content 'Opti Détaillés\Optimisations Avancées\Mémoire Avancée\Menu Mémoire.bat') -replace 'MathysM', 'MZEER' -replace 'beacons\.ai/mathysm', 'https://www.twitch.tv/mzeer_' | Set-Content 'Opti Détaillés\Optimisations Avancée\Mémoire Avancée\Menu Mémoire.bat'"

powershell -Command "(Get-Content 'Opti Détaillés\Optimisations Avancées\CPU Avancé\Menu CPU.bat') -replace 'MathysM', 'MZEER' -replace 'beacons\.ai/mathysm', 'https://www.twitch.tv/mzeer_' | Set-Content 'Opti Détaillés\Optimisations Avancées\CPU Avancé\Menu CPU.bat'"

powershell -Command "(Get-Content 'Opti Détaillés\Optimisations Avancées\Gaming Ultra Performance\Menu Gaming Ultra.bat') -replace 'MathysM', 'MZEER' -replace 'beacons\.ai/mathysm', 'https://www.twitch.tv/mzeer_' | Set-Content 'Opti Détaillés\Optimisations Avancées\Gaming Ultra Performance\Menu Gaming Ultra.bat'"

powershell -Command "(Get-Content 'Opti Détaillés\Optimisations Avancées\Scripts\Optimisation Complète.bat') -replace 'MathysM', 'MZEER' -replace 'beacons\.ai/mathysm', 'https://www.twitch.tv/mzeer_' | Set-Content 'Opti Détaillés\Optimisations Avancées\Scripts\Optimisation Complète.bat'"

echo 📁 Mise à jour des fichiers PowerShell...
powershell -Command "(Get-Content 'Ressources\Gaming Ultra\Enable MSI Mode GPU.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Gaming Ultra\Enable MSI Mode GPU.ps1'"

powershell -Command "(Get-Content 'Ressources\Gaming Ultra\Set Timer Resolution Ultra.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Gaming Ultra\Set Timer Resolution Ultra.ps1'"

powershell -Command "(Get-Content 'Ressources\Gaming Ultra\NVIDIA Ultra Settings.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Gaming Ultra\NVIDIA Ultra Settings.ps1'"

powershell -Command "(Get-Content 'Ressources\Gaming Ultra\Memory Ultra Tuning.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Gaming Ultra\Memory Ultra Tuning.ps1'"

powershell -Command "(Get-Content 'Ressources\Gaming Ultra\CPU Micro Optimizations.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Gaming Ultra\CPU Micro Optimizations.ps1'"

powershell -Command "(Get-Content 'Ressources\Mémoire\Set Gaming Paging File.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Mémoire\Set Gaming Paging File.ps1'"

powershell -Command "(Get-Content 'Ressources\Mémoire\Set Balanced Paging File.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Mémoire\Set Balanced Paging File.ps1'"

powershell -Command "(Get-Content 'Ressources\Mémoire\Enable Large Pages.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Mémoire\Enable Large Pages.ps1'"

powershell -Command "(Get-Content 'Ressources\Mémoire\Memory Cleanup.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Mémoire\Memory Cleanup.ps1'"

powershell -Command "(Get-Content 'Ressources\Mémoire\Restore Default Paging File.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Mémoire\Restore Default Paging File.ps1'"

powershell -Command "(Get-Content 'Ressources\Réseau\Disable Network Power Management.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Réseau\Disable Network Power Management.ps1'"

powershell -Command "(Get-Content 'Ressources\Réseau\Enable Network Power Management.ps1') -replace 'MathysM', 'MZEER' | Set-Content 'Ressources\Réseau\Enable Network Power Management.ps1'"

echo.
echo ✅ Mise à jour terminée!
echo • MathysM → MZEER
echo • beacons.ai/mathysm → https://www.twitch.tv/mzeer_
echo.
echo Tous les fichiers ont été mis à jour avec le nouveau branding MZEER.
echo.
pause
