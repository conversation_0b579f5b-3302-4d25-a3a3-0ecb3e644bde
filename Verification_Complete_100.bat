@echo off

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Droits administrateur requis. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

title Verification Complete 100%% - MZEER Edition

echo.
echo ===============================================================================
echo                    VERIFICATION COMPLETE 100%% - MZEER EDITION               
echo ===============================================================================
echo.
echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo Ce script verifie que TOUTES les optimisations sont appliquees a 100%%
echo.

set /a total_optimisations=0
set /a optimisations_ok=0
set /a optimisations_echec=0

echo ========================================
echo [1/8] VERIFICATION RESEAU
echo ========================================
echo.

echo [1.1] Network Throttling Index:
set /a total_optimisations+=1
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295"
if %ERRORLEVEL% == 0 (
    echo [OK] Network Throttling DESACTIVE (4294967295)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Network Throttling non configure ou valeur incorrecte
    set /a optimisations_echec+=1
    reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul
)

echo.
echo [1.2] System Responsiveness:
set /a total_optimisations+=1
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "SystemResponsiveness" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] System Responsiveness configure (0)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] System Responsiveness non configure
    set /a optimisations_echec+=1
)

echo.
echo [1.3] TCP Gaming Parameters:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TcpAckFrequency" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] TcpAckFrequency configure (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] TcpAckFrequency non configure
    set /a optimisations_echec+=1
)

echo.
echo [1.4] QoS Gaming Priority:
set /a total_optimisations+=1
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" 2>nul | findstr /i "0x6"
if %ERRORLEVEL% == 0 (
    echo [OK] QoS Gaming Priority configure (6)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] QoS Gaming Priority non configure
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [2/8] VERIFICATION MEMOIRE
echo ========================================
echo.

echo [2.1] Disable Paging Executive:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Paging Executive desactive (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Paging Executive non desactive
    set /a optimisations_echec+=1
)

echo.
echo [2.2] Large Page Support:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargePageMinimum" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] Large Page Support active (0)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Large Page Support non configure
    set /a optimisations_echec+=1
)

echo.
echo [2.3] Memory Management Features:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "FeatureSettings" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Memory Management Features configure (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Memory Management Features non configure
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [3/8] VERIFICATION CPU
echo ========================================
echo.

echo [3.1] CPU Scheduling Gaming:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26"
if %ERRORLEVEL% == 0 (
    echo [OK] CPU Scheduling Gaming configure (38/0x26)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] CPU Scheduling Gaming non configure
    set /a optimisations_echec+=1
    reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul
)

echo.
echo [3.2] Core Parking (verification PowerCfg):
set /a total_optimisations+=1
powercfg /query SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 | findstr /i "0x00000000"
if %ERRORLEVEL% == 0 (
    echo [OK] Core Parking desactive (0)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Core Parking non desactive
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [4/8] VERIFICATION GAMING
echo ========================================
echo.

echo [4.1] Timer Resolution Ultra:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Timer Resolution Ultra active (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Timer Resolution Ultra non active
    set /a optimisations_echec+=1
)

echo.
echo [4.2] Game Mode Auto:
set /a total_optimisations+=1
reg query "HKCU\Software\Microsoft\GameBar" /v "AutoGameModeEnabled" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Game Mode Auto active (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Game Mode Auto non active
    set /a optimisations_echec+=1
)

echo.
echo [4.3] Game DVR Disabled:
set /a total_optimisations+=1
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] Game DVR desactive (0)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Game DVR non desactive
    set /a optimisations_echec+=1
)

echo.
echo [4.4] Hardware GPU Scheduling:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2"
if %ERRORLEVEL% == 0 (
    echo [OK] Hardware GPU Scheduling active (2)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Hardware GPU Scheduling non active
    set /a optimisations_echec+=1
)

echo.
echo [4.5] GPU TDR Disabled:
set /a total_optimisations+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] GPU TDR desactive (0)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] GPU TDR non desactive
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [5/8] VERIFICATION SERVICES
echo ========================================
echo.

echo [5.1] Windows Search Service:
set /a total_optimisations+=1
sc query "WSearch" | findstr /i "STOPPED"
if %ERRORLEVEL% == 0 (
    echo [OK] Windows Search arrete
    sc qc "WSearch" | findstr /i "DISABLED"
    if %ERRORLEVEL% == 0 (
        echo [OK] Windows Search desactive
        set /a optimisations_ok+=1
    ) else (
        echo [ECHEC] Windows Search non desactive
        set /a optimisations_echec+=1
    )
) else (
    echo [ECHEC] Windows Search encore actif
    set /a optimisations_echec+=1
)

echo.
echo [5.2] Print Spooler Service:
set /a total_optimisations+=1
sc qc "Spooler" | findstr /i "DISABLED"
if %ERRORLEVEL% == 0 (
    echo [OK] Print Spooler desactive
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Print Spooler non desactive
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [6/8] VERIFICATION STOCKAGE
echo ========================================
echo.

echo [6.1] Last Access Time:
set /a total_optimisations+=1
fsutil behavior query DisableLastAccess | findstr /i "DisableLastAccess = 1"
if %ERRORLEVEL% == 0 (
    echo [OK] Last Access Time desactive
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Last Access Time non desactive
    set /a optimisations_echec+=1
)

echo.
echo [6.2] TRIM SSD:
set /a total_optimisations+=1
fsutil behavior query DisableDeleteNotify | findstr /i "NTFS DisableDeleteNotify = 0"
if %ERRORLEVEL% == 0 (
    echo [OK] TRIM SSD active
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] TRIM SSD non active
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [7/8] VERIFICATION AUDIO
echo ========================================
echo.

echo [7.1] Audio Protected DG:
set /a total_optimisations+=1
reg query "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Audio" /v "DisableProtectedAudioDG" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Audio Protected DG desactive (1)
    set /a optimisations_ok+=1
) else (
    echo [ECHEC] Audio Protected DG non desactive
    set /a optimisations_echec+=1
)

echo.
echo ========================================
echo [8/8] VERIFICATION NETTOYAGE
echo ========================================
echo.

echo [8.1] Verification espace disque libre:
powershell -Command "$free = (Get-WmiObject -Class Win32_LogicalDisk -Filter \"DeviceID='C:'\").FreeSpace / 1GB; Write-Host \"Espace libre C: $([math]::Round($free, 2)) GB\""

echo.
echo [8.2] Verification fichiers temp:
if exist "%temp%\*.*" (
    for /f %%i in ('dir /b "%temp%\*.*" 2^>nul ^| find /c /v ""') do (
        if %%i LEQ 5 (
            echo [OK] Temp utilisateur propre (%%i fichiers)
        ) else (
            echo [INFO] Temp utilisateur: %%i fichiers restants
        )
    )
) else (
    echo [PARFAIT] Temp utilisateur completement vide
)

echo.
echo ===============================================================================
echo                              RESUME FINAL 100%%                             
echo ===============================================================================
echo.

set /a pourcentage_reussite=(%optimisations_ok% * 100) / %total_optimisations%

echo TOTAL OPTIMISATIONS TESTEES: %total_optimisations%
echo OPTIMISATIONS REUSSIES: %optimisations_ok%
echo OPTIMISATIONS ECHOUEES: %optimisations_echec%
echo.
echo TAUX DE REUSSITE: %pourcentage_reussite%%%
echo.

if %pourcentage_reussite% GEQ 95 (
    echo [EXCELLENT] Votre systeme est optimise a %pourcentage_reussite%%% !
    echo Toutes les optimisations principales fonctionnent parfaitement !
    echo Vous devriez constater des gains FPS significatifs !
) else if %pourcentage_reussite% GEQ 80 (
    echo [BIEN] Votre systeme est optimise a %pourcentage_reussite%%% !
    echo La plupart des optimisations fonctionnent.
    echo Un redemarrage peut etre necessaire pour certaines.
) else if %pourcentage_reussite% GEQ 60 (
    echo [MOYEN] Votre systeme est optimise a %pourcentage_reussite%%% !
    echo Plusieurs optimisations ont echoue.
    echo Relancez l'optimiseur en tant qu'administrateur.
) else (
    echo [ATTENTION] Votre systeme est optimise a seulement %pourcentage_reussite%%% !
    echo La plupart des optimisations ont echoue.
    echo Verifiez les droits administrateur et relancez l'optimiseur.
)

echo.
echo ===============================================================================
echo.
echo Verification complete terminee !
echo Merci d'avoir utilise MZEER Gaming Optimizer !
echo https://www.twitch.tv/mzeer_
echo.
pause
