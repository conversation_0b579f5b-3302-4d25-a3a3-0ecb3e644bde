@echo off
chcp 65001
set "base_path=%~dp0..\..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations Réseau & Latence

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███╗   ██╗███████╗████████╗██╗    ██╗ ██████╗ ██████╗ ██╗  ██╗
echo       ████╗  ██║██╔════╝╚══██╔══╝██║    ██║██╔═══██╗██╔══██╗██║ ██╔╝
echo       ██╔██╗ ██║█████╗     ██║   ██║ █╗ ██║██║   ██║██████╔╝█████╔╝ 
echo       ██║╚██╗██║██╔══╝     ██║   ██║███╗██║██║   ██║██╔══██╗██╔═██╗ 
echo       ██║ ╚████║███████╗   ██║   ╚███╔███╔╝╚██████╔╝██║  ██║██║  ██╗
echo       ╚═╝  ╚═══╝╚══════╝   ╚═╝    ╚══╝╚══╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝
echo.
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        OPTIMISATIONS RÉSEAU & LATENCE                       ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. 🚀 TCP/IP Stack Optimization     │  6. 📡 Network Adapter Power        ║
echo ║  2. 🌐 DNS Optimization              │  7. 🎯 Optimisation Complète        ║
echo ║  3. ⚡ Network Throttling             │  8. 🔄 Restaurer Réseau             ║
echo ║  4. 🔧 Network Buffer Optimization   │  9. ℹ️  Test de Latence             ║
echo ║  5. 🎮 Gaming Network Priority       │  0. 🚪 Retour Menu Principal        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890 /M "Sélectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO TestLatence
IF ERRORLEVEL 8 GOTO RestaurerReseau
IF ERRORLEVEL 7 GOTO OptimisationCompleteReseau
IF ERRORLEVEL 6 GOTO NetworkAdapterPower
IF ERRORLEVEL 5 GOTO GamingNetworkPriority
IF ERRORLEVEL 4 GOTO NetworkBufferOptimization
IF ERRORLEVEL 3 GOTO NetworkThrottling
IF ERRORLEVEL 2 GOTO DNSOptimization
IF ERRORLEVEL 1 GOTO TCPIPOptimization

:TCPIPOptimization
CLS
title TCP/IP Stack Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          TCP/IP STACK OPTIMIZATION                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser les paramètres TCP/IP pour réduire la latence
echo • Configurer les buffers réseau pour de meilleures performances
echo • Ajuster les timeouts TCP pour une meilleure réactivité
echo • Optimiser la fenêtre de réception TCP
echo.
choice /c:yn /n /m "Appliquer l'optimisation TCP/IP? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyTCPIP
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyTCPIP
echo.
echo Application des optimisations TCP/IP...
regedit /s "%base_path%Ressources\Réseau\TCP-IP Optimization.reg"
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled
echo.
echo ✅ Optimisations TCP/IP appliquées avec succès!
echo.
echo Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:DNSOptimization
CLS
title DNS Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            DNS OPTIMIZATION                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Choisissez votre configuration DNS:
echo.
echo 1. 🚀 Cloudflare (*******) - Le plus rapide
echo 2. 🌐 Google (*******) - Fiable et rapide
echo 3. 🔒 Quad9 (*******) - Sécurisé et rapide
echo 4. 🏠 DNS Automatique (par défaut)
echo 5. 🚪 Retour au menu
echo.

CHOICE /C 12345 /M "Sélectionnez votre DNS:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO DNSAuto
IF ERRORLEVEL 3 GOTO DNSQuad9
IF ERRORLEVEL 2 GOTO DNSGoogle
IF ERRORLEVEL 1 GOTO DNSCloudflare

:DNSCloudflare
echo.
echo Configuration DNS Cloudflare...
netsh interface ip set dns "Ethernet" static *******
netsh interface ip add dns "Ethernet" ******* index=2
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" ******* index=2
echo ✅ DNS Cloudflare configuré!
GOTO DNSComplete

:DNSGoogle
echo.
echo Configuration DNS Google...
netsh interface ip set dns "Ethernet" static *******
netsh interface ip add dns "Ethernet" ******* index=2
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" ******* index=2
echo ✅ DNS Google configuré!
GOTO DNSComplete

:DNSQuad9
echo.
echo Configuration DNS Quad9...
netsh interface ip set dns "Ethernet" static *******
netsh interface ip add dns "Ethernet" *************** index=2
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" *************** index=2
echo ✅ DNS Quad9 configuré!
GOTO DNSComplete

:DNSAuto
echo.
echo Restauration DNS automatique...
netsh interface ip set dns "Ethernet" dhcp
netsh interface ip set dns "Wi-Fi" dhcp
echo ✅ DNS automatique restauré!
GOTO DNSComplete

:DNSComplete
echo.
echo Vidage du cache DNS...
ipconfig /flushdns
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:NetworkThrottling
CLS
title Network Throttling Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        NETWORK THROTTLING OPTIMIZATION                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Désactiver le throttling réseau Windows
echo • Optimiser la QoS (Quality of Service)
echo • Améliorer les performances réseau pour les jeux
echo • Réduire la latence réseau
echo.
choice /c:yn /n /m "Désactiver le throttling réseau? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyNetworkThrottling
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyNetworkThrottling
echo.
echo Désactivation du throttling réseau...
regedit /s "%base_path%Ressources\Réseau\Disable Network Throttling.reg"
netsh int tcp set global nonsackrttresiliency=disabled
netsh int tcp set global maxsynretransmissions=2
echo.
echo ✅ Throttling réseau désactivé avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:NetworkBufferOptimization
CLS
title Network Buffer Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        NETWORK BUFFER OPTIMIZATION                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser les buffers de réception et d'envoi
echo • Ajuster la taille des fenêtres TCP
echo • Améliorer le débit réseau
echo • Réduire les pertes de paquets
echo.
choice /c:yn /n /m "Optimiser les buffers réseau? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyNetworkBuffer
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyNetworkBuffer
echo.
echo Optimisation des buffers réseau...
regedit /s "%base_path%Ressources\Réseau\Network Buffer Optimization.reg"
netsh int tcp set global ecncapability=enabled
netsh int tcp set global timestamps=enabled
echo.
echo ✅ Buffers réseau optimisés avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:GamingNetworkPriority
CLS
title Gaming Network Priority

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                         GAMING NETWORK PRIORITY                             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Donner la priorité au trafic gaming
echo • Optimiser la QoS pour les jeux
echo • Réduire la latence en jeu
echo • Améliorer la stabilité de connexion
echo.
choice /c:yn /n /m "Activer la priorité réseau gaming? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyGamingPriority
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyGamingPriority
echo.
echo Configuration de la priorité réseau gaming...
regedit /s "%base_path%Ressources\Réseau\Gaming Network Priority.reg"
netsh int tcp set global dca=enabled
powershell -Command "Set-NetQosPolicy -Name 'Gaming' -AppPathNameMatchCondition '*.exe' -NetworkProfile All -PriorityValue8021Action 7"
echo.
echo ✅ Priorité réseau gaming activée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:NetworkAdapterPower
CLS
title Network Adapter Power Management

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    NETWORK ADAPTER POWER MANAGEMENT                         ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Désactiver la gestion d'énergie des cartes réseau
echo • Empêcher la mise en veille des adaptateurs
echo • Améliorer la stabilité de connexion
echo • Réduire les déconnexions intempestives
echo.
choice /c:yn /n /m "Désactiver la gestion d'énergie réseau? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyNetworkPower
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyNetworkPower
echo.
echo Désactivation de la gestion d'énergie réseau...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Réseau\Disable Network Power Management.ps1"
echo.
echo ✅ Gestion d'énergie réseau désactivée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:OptimisationCompleteReseau
CLS
title Optimisation Complète Réseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        OPTIMISATION COMPLÈTE RÉSEAU                         ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option appliquera TOUTES les optimisations réseau:
echo • TCP/IP Stack Optimization
echo • DNS Cloudflare (*******)
echo • Désactivation Network Throttling
echo • Optimisation Network Buffer
echo • Priorité Gaming Network
echo • Désactivation Power Management
echo.
choice /c:yn /n /m "Appliquer toutes les optimisations réseau? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyAllNetwork
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyAllNetwork
echo.
echo Application de toutes les optimisations réseau...
echo.
echo [1/6] TCP/IP Stack Optimization...
regedit /s "%base_path%Ressources\Réseau\TCP-IP Optimization.reg"
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled
echo.
echo [2/6] Configuration DNS Cloudflare...
netsh interface ip set dns "Ethernet" static *******
netsh interface ip add dns "Ethernet" ******* index=2
netsh interface ip set dns "Wi-Fi" static *******
netsh interface ip add dns "Wi-Fi" ******* index=2
ipconfig /flushdns
echo.
echo [3/6] Désactivation Network Throttling...
regedit /s "%base_path%Ressources\Réseau\Disable Network Throttling.reg"
netsh int tcp set global nonsackrttresiliency=disabled
netsh int tcp set global maxsynretransmissions=2
echo.
echo [4/6] Optimisation Network Buffer...
regedit /s "%base_path%Ressources\Réseau\Network Buffer Optimization.reg"
netsh int tcp set global ecncapability=enabled
netsh int tcp set global timestamps=enabled
echo.
echo [5/6] Configuration Gaming Network Priority...
regedit /s "%base_path%Ressources\Réseau\Gaming Network Priority.reg"
netsh int tcp set global dca=enabled
echo.
echo [6/6] Désactivation Network Power Management...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Réseau\Disable Network Power Management.ps1"
echo.
echo ✅ TOUTES les optimisations réseau ont été appliquées avec succès!
echo.
echo ⚠️  Redémarrage fortement recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:RestaurerReseau
CLS
title Restauration Réseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            RESTAURATION RÉSEAU                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option restaurera tous les paramètres réseau par défaut:
echo • Paramètres TCP/IP par défaut
echo • DNS automatique
echo • Réactivation Network Throttling
echo • Buffers réseau par défaut
echo • Priorité réseau normale
echo • Réactivation Power Management
echo.
choice /c:yn /n /m "Restaurer tous les paramètres réseau? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO RestoreAllNetwork
if %ERRORLEVEL% == 2 GOTO Menu

:RestoreAllNetwork
echo.
echo Restauration de tous les paramètres réseau...
echo.
echo [1/6] Restauration TCP/IP...
regedit /s "%base_path%Ressources\Réseau\Restore TCP-IP.reg"
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=disabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=disabled
echo.
echo [2/6] Restauration DNS automatique...
netsh interface ip set dns "Ethernet" dhcp
netsh interface ip set dns "Wi-Fi" dhcp
ipconfig /flushdns
echo.
echo [3/6] Réactivation Network Throttling...
regedit /s "%base_path%Ressources\Réseau\Enable Network Throttling.reg"
netsh int tcp set global nonsackrttresiliency=enabled
echo.
echo [4/6] Restauration Network Buffer...
regedit /s "%base_path%Ressources\Réseau\Restore Network Buffer.reg"
netsh int tcp set global ecncapability=disabled
netsh int tcp set global timestamps=disabled
echo.
echo [5/6] Restauration Gaming Network Priority...
regedit /s "%base_path%Ressources\Réseau\Restore Gaming Priority.reg"
netsh int tcp set global dca=disabled
powershell -Command "Remove-NetQosPolicy -Name 'Gaming' -Confirm:$false" 2>nul
echo.
echo [6/6] Réactivation Network Power Management...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Réseau\Enable Network Power Management.ps1"
echo.
echo ✅ TOUS les paramètres réseau ont été restaurés avec succès!
echo.
echo ⚠️  Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:TestLatence
CLS
title Test de Latence

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              TEST DE LATENCE                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Test de latence vers différents serveurs...
echo.
echo 🌐 Test Cloudflare (*******):
ping -n 4 *******
echo.
echo 🌐 Test Google (*******):
ping -n 4 *******
echo.
echo 🎮 Test serveur gaming (Valve):
ping -n 4 steamcommunity.com
echo.
echo 🎮 Test serveur gaming (Riot):
ping -n 4 riot.com
echo.
echo Appuyez sur une touche pour revenir au menu...
pause >nul
GOTO Menu

:Retour
exit
