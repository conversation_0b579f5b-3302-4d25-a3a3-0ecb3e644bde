@echo off
chcp 65001
set "base_path=%~dp0..\..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations CPU Avancé

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo        ██████╗██████╗ ██╗   ██╗     █████╗ ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗ ██████╗███████╗
echo       ██╔════╝██╔══██╗██║   ██║    ██╔══██╗██╔══██╗██║   ██║██╔══██╗████╗  ██║██╔════╝██╔════╝
echo       ██║     ██████╔╝██║   ██║    ███████║██║  ██║██║   ██║███████║██╔██╗ ██║██║     █████╗  
echo       ██║     ██╔═══╝ ██║   ██║    ██╔══██║██║  ██║╚██╗ ██╔╝██╔══██║██║╚██╗██║██║     ██╔══╝  
echo       ╚██████╗██║     ╚██████╔╝    ██║  ██║██████╔╝ ╚████╔╝ ██║  ██║██║ ╚████║╚██████╗███████╗
echo        ╚═════╝╚═╝      ╚═════╝     ╚═╝  ╚═╝╚═════╝   ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝
echo.
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          OPTIMISATIONS CPU AVANCÉ                           ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. ⚡ CPU Scheduling                │  6. 📊 Analyse CPU                   ║
echo ║  2. 🚀 Core Parking                 │  7. 🎯 Optimisation Complète         ║
echo ║  3. 🎯 CPU Priority Classes          │  8. 🔄 Restaurer CPU                 ║
echo ║  4. 🔧 Interrupt Affinity            │  9. 🌡️  Monitoring Température       ║
echo ║  5. ⚙️  Power Management CPU         │  0. 🚪 Retour Menu Principal         ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890 /M "Sélectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO MonitoringTemperature
IF ERRORLEVEL 8 GOTO RestaurerCPU
IF ERRORLEVEL 7 GOTO OptimisationCompleteCPU
IF ERRORLEVEL 6 GOTO AnalyseCPU
IF ERRORLEVEL 5 GOTO PowerManagementCPU
IF ERRORLEVEL 4 GOTO InterruptAffinity
IF ERRORLEVEL 3 GOTO CPUPriorityClasses
IF ERRORLEVEL 2 GOTO CoreParking
IF ERRORLEVEL 1 GOTO CPUScheduling

:CPUScheduling
CLS
title CPU Scheduling Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        CPU SCHEDULING OPTIMIZATION                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser le planificateur de processus Windows
echo • Améliorer la répartition des tâches sur les cœurs CPU
echo • Réduire la latence de commutation des threads
echo • Optimiser les priorités de planification
echo.
choice /c:yn /n /m "Optimiser le planificateur CPU? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyCPUScheduling
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyCPUScheduling
echo.
echo Optimisation du planificateur CPU...
regedit /s "%base_path%Ressources\CPU\CPU Scheduling Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Optimize CPU Scheduling.ps1"
echo.
echo ✅ Planificateur CPU optimisé avec succès!
echo.
echo Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:CoreParking
CLS
title Core Parking Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                         CORE PARKING OPTIMIZATION                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Choisissez votre configuration de Core Parking:
echo.
echo 1. 🚀 Gaming (Désactiver Core Parking)
echo 2. ⚡ Performance (Core Parking Minimal)
echo 3. ⚖️  Équilibré (Core Parking Modéré)
echo 4. 🔋 Économie d'énergie (Core Parking Actif)
echo 5. 🚪 Retour au menu
echo.

CHOICE /C 12345 /M "Sélectionnez votre configuration:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO CoreParkingEco
IF ERRORLEVEL 3 GOTO CoreParkingBalanced
IF ERRORLEVEL 2 GOTO CoreParkingPerformance
IF ERRORLEVEL 1 GOTO CoreParkingGaming

:CoreParkingGaming
echo.
echo Configuration Core Parking Gaming (Désactivé)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Gaming configuré!
GOTO CoreParkingComplete

:CoreParkingPerformance
echo.
echo Configuration Core Parking Performance (Minimal)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Performance configuré!
GOTO CoreParkingComplete

:CoreParkingBalanced
echo.
echo Configuration Core Parking Équilibré...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Équilibré configuré!
GOTO CoreParkingComplete

:CoreParkingEco
echo.
echo Configuration Core Parking Économie d'énergie...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Économie configuré!
GOTO CoreParkingComplete

:CoreParkingComplete
echo.
echo Configuration Core Parking appliquée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu
