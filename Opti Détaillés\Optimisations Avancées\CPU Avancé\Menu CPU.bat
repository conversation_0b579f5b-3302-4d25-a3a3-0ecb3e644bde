@echo off
chcp 65001
set "base_path=%~dp0..\..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations CPU Avancé

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo        ██████╗██████╗ ██╗   ██╗     █████╗ ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗ ██████╗███████╗
echo       ██╔════╝██╔══██╗██║   ██║    ██╔══██╗██╔══██╗██║   ██║██╔══██╗████╗  ██║██╔════╝██╔════╝
echo       ██║     ██████╔╝██║   ██║    ███████║██║  ██║██║   ██║███████║██╔██╗ ██║██║     █████╗  
echo       ██║     ██╔═══╝ ██║   ██║    ██╔══██║██║  ██║╚██╗ ██╔╝██╔══██║██║╚██╗██║██║     ██╔══╝  
echo       ╚██████╗██║     ╚██████╔╝    ██║  ██║██████╔╝ ╚████╔╝ ██║  ██║██║ ╚████║╚██████╗███████╗
echo        ╚═════╝╚═╝      ╚═════╝     ╚═╝  ╚═╝╚═════╝   ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝
echo.
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          OPTIMISATIONS CPU AVANCÉ                           ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. ⚡ CPU Scheduling                │  6. 📊 Analyse CPU                   ║
echo ║  2. 🚀 Core Parking                 │  7. 🎯 Optimisation Complète         ║
echo ║  3. 🎯 CPU Priority Classes          │  8. 🔄 Restaurer CPU                 ║
echo ║  4. 🔧 Interrupt Affinity            │  9. 🌡️  Monitoring Température       ║
echo ║  5. ⚙️  Power Management CPU         │  0. 🚪 Retour Menu Principal         ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890 /M "Sélectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO MonitoringTemperature
IF ERRORLEVEL 8 GOTO RestaurerCPU
IF ERRORLEVEL 7 GOTO OptimisationCompleteCPU
IF ERRORLEVEL 6 GOTO AnalyseCPU
IF ERRORLEVEL 5 GOTO PowerManagementCPU
IF ERRORLEVEL 4 GOTO InterruptAffinity
IF ERRORLEVEL 3 GOTO CPUPriorityClasses
IF ERRORLEVEL 2 GOTO CoreParking
IF ERRORLEVEL 1 GOTO CPUScheduling

:CPUScheduling
CLS
title CPU Scheduling Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        CPU SCHEDULING OPTIMIZATION                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser le planificateur de processus Windows
echo • Améliorer la répartition des tâches sur les cœurs CPU
echo • Réduire la latence de commutation des threads
echo • Optimiser les priorités de planification
echo.
choice /c:yn /n /m "Optimiser le planificateur CPU? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyCPUScheduling
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyCPUScheduling
echo.
echo Optimisation du planificateur CPU...
regedit /s "%base_path%Ressources\CPU\CPU Scheduling Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Optimize CPU Scheduling.ps1"
echo.
echo ✅ Planificateur CPU optimisé avec succès!
echo.
echo Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:CoreParking
CLS
title Core Parking Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                         CORE PARKING OPTIMIZATION                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Choisissez votre configuration de Core Parking:
echo.
echo 1. 🚀 Gaming (Désactiver Core Parking)
echo 2. ⚡ Performance (Core Parking Minimal)
echo 3. ⚖️  Équilibré (Core Parking Modéré)
echo 4. 🔋 Économie d'énergie (Core Parking Actif)
echo 5. 🚪 Retour au menu
echo.

CHOICE /C 12345 /M "Sélectionnez votre configuration:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO CoreParkingEco
IF ERRORLEVEL 3 GOTO CoreParkingBalanced
IF ERRORLEVEL 2 GOTO CoreParkingPerformance
IF ERRORLEVEL 1 GOTO CoreParkingGaming

:CoreParkingGaming
echo.
echo Configuration Core Parking Gaming (Désactivé)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Gaming configuré!
GOTO CoreParkingComplete

:CoreParkingPerformance
echo.
echo Configuration Core Parking Performance (Minimal)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Performance configuré!
GOTO CoreParkingComplete

:CoreParkingBalanced
echo.
echo Configuration Core Parking Équilibré...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Équilibré configuré!
GOTO CoreParkingComplete

:CoreParkingEco
echo.
echo Configuration Core Parking Économie d'énergie...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75
powercfg -setactive SCHEME_CURRENT
echo ✅ Core Parking Économie configuré!
GOTO CoreParkingComplete

:CoreParkingComplete
echo.
echo Configuration Core Parking appliquée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:CPUPriorityClasses
CLS
title CPU Priority Classes

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          CPU PRIORITY CLASSES                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Configurer les classes de priorité CPU
echo • Optimiser la répartition des ressources processeur
echo • Améliorer les performances des applications critiques
echo • Réduire les conflits de priorité
echo.
choice /c:yn /n /m "Configurer les classes de priorité CPU? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyCPUPriority
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyCPUPriority
echo.
echo Configuration des classes de priorité CPU...
regedit /s "%base_path%Ressources\CPU\CPU Priority Classes.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Configure CPU Priority.ps1"
echo.
echo ✅ Classes de priorité CPU configurées avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:InterruptAffinity
CLS
title Interrupt Affinity Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                       INTERRUPT AFFINITY OPTIMIZATION                       ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Cette optimisation va:
echo • Optimiser l'affinité des interruptions
echo • Répartir les interruptions sur les cœurs CPU
echo • Réduire la latence des interruptions
echo • Améliorer les performances gaming
echo.
choice /c:yn /n /m "Optimiser l'affinité des interruptions? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyInterruptAffinity
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyInterruptAffinity
echo.
echo Optimisation de l'affinité des interruptions...
regedit /s "%base_path%Ressources\CPU\Interrupt Affinity Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Optimize Interrupt Affinity.ps1"
echo.
echo ✅ Affinité des interruptions optimisée avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:PowerManagementCPU
CLS
title CPU Power Management

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          CPU POWER MANAGEMENT                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Choisissez votre profil de gestion d'énergie CPU:
echo.
echo 1. 🚀 Gaming Ultra (Performance Maximale)
echo 2. ⚡ Haute Performance
echo 3. ⚖️  Équilibré
echo 4. 🔋 Économie d'énergie
echo 5. 🚪 Retour au menu
echo.

CHOICE /C 12345 /M "Sélectionnez votre profil:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO PowerEco
IF ERRORLEVEL 3 GOTO PowerBalanced
IF ERRORLEVEL 2 GOTO PowerHigh
IF ERRORLEVEL 1 GOTO PowerGaming

:PowerGaming
echo.
echo Configuration Gaming Ultra (Performance Maximale)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100
powercfg -setactive SCHEME_CURRENT
echo ✅ Profil Gaming Ultra configuré!
GOTO PowerComplete

:PowerHigh
echo.
echo Configuration Haute Performance...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 90
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 90
powercfg -setactive SCHEME_CURRENT
echo ✅ Profil Haute Performance configuré!
GOTO PowerComplete

:PowerBalanced
echo.
echo Configuration Équilibrée...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 94
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 75
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 5
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 5
powercfg -setactive SCHEME_CURRENT
echo ✅ Profil Équilibré configuré!
GOTO PowerComplete

:PowerEco
echo.
echo Configuration Économie d'énergie...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 50
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 50
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 0
powercfg -setactive SCHEME_CURRENT
echo ✅ Profil Économie configuré!
GOTO PowerComplete

:PowerComplete
echo.
echo Configuration de gestion d'énergie CPU appliquée!
echo.
echo Retour au menu dans:
for /L %%i in (5,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:AnalyseCPU
CLS
title Analyse CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                               ANALYSE CPU                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Analyse des performances CPU en cours...
echo.
echo Informations CPU:
powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed, CurrentClockSpeed, LoadPercentage | Format-List"
echo.
echo Utilisation CPU par processus:
powershell -Command "Get-Process | Sort-Object CPU -Descending | Select-Object -First 10 Name, @{Name='CPU (%)';Expression={[math]::Round($_.CPU,2)}}, @{Name='Mémoire (MB)';Expression={[math]::Round($_.WorkingSet/1MB,2)}} | Format-Table -AutoSize"
echo.
echo Température CPU (si disponible):
powershell -Command "try { Get-WmiObject -Namespace 'root\wmi' -Class MSAcpi_ThermalZoneTemperature | Select-Object @{Name='Température (°C)';Expression={($_.CurrentTemperature/10)-273.15}} | Format-Table -AutoSize } catch { Write-Host 'Capteur de température non disponible' }"
echo.
echo Appuyez sur une touche pour revenir au menu...
pause >nul
GOTO Menu

:OptimisationCompleteCPU
CLS
title Optimisation Complète CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        OPTIMISATION COMPLÈTE CPU                            ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option appliquera TOUTES les optimisations CPU:
echo • CPU Scheduling Optimization
echo • Core Parking Gaming (Désactivé)
echo • CPU Priority Classes
echo • Interrupt Affinity Optimization
echo • CPU Power Management Gaming Ultra
echo.
choice /c:yn /n /m "Appliquer toutes les optimisations CPU? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ApplyAllCPU
if %ERRORLEVEL% == 2 GOTO Menu

:ApplyAllCPU
echo.
echo Application de toutes les optimisations CPU...
echo.
echo [1/5] CPU Scheduling Optimization...
regedit /s "%base_path%Ressources\CPU\CPU Scheduling Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Optimize CPU Scheduling.ps1"
echo.
echo [2/5] Core Parking Gaming (Désactivé)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0
echo.
echo [3/5] CPU Priority Classes...
regedit /s "%base_path%Ressources\CPU\CPU Priority Classes.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Configure CPU Priority.ps1"
echo.
echo [4/5] Interrupt Affinity Optimization...
regedit /s "%base_path%Ressources\CPU\Interrupt Affinity Optimization.reg"
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\CPU\Optimize Interrupt Affinity.ps1"
echo.
echo [5/5] CPU Power Management Gaming Ultra...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100
powercfg -setactive SCHEME_CURRENT
echo.
echo ✅ TOUTES les optimisations CPU ont été appliquées avec succès!
echo.
echo ⚠️  Redémarrage recommandé pour appliquer tous les changements.
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:RestaurerCPU
CLS
title Restauration CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                             RESTAURATION CPU                                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ⚠️  Cette option restaurera tous les paramètres CPU par défaut:
echo • Planificateur CPU par défaut
echo • Core Parking automatique
echo • Priorités CPU par défaut
echo • Affinité des interruptions par défaut
echo • Gestion d'énergie équilibrée
echo.
choice /c:yn /n /m "Restaurer tous les paramètres CPU? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO RestoreAllCPU
if %ERRORLEVEL% == 2 GOTO Menu

:RestoreAllCPU
echo.
echo Restauration de tous les paramètres CPU...
echo.
echo [1/5] Restauration CPU Scheduling...
regedit /s "%base_path%Ressources\CPU\Restore CPU Scheduling.reg"
echo.
echo [2/5] Restauration Core Parking...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2
echo.
echo [3/5] Restauration CPU Priority Classes...
regedit /s "%base_path%Ressources\CPU\Restore CPU Priority.reg"
echo.
echo [4/5] Restauration Interrupt Affinity...
regedit /s "%base_path%Ressources\CPU\Restore Interrupt Affinity.reg"
echo.
echo [5/5] Restauration Power Management...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 94
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 75
powercfg -setactive SCHEME_CURRENT
echo.
echo ✅ TOUS les paramètres CPU ont été restaurés avec succès!
echo.
echo Retour au menu dans:
for /L %%i in (10,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:MonitoringTemperature
CLS
title Monitoring Température CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                         MONITORING TEMPÉRATURE CPU                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo Surveillance de la température CPU en temps réel...
echo Appuyez sur Ctrl+C pour arrêter le monitoring.
echo.

:TempLoop
powershell -Command "try { $temp = Get-WmiObject -Namespace 'root\wmi' -Class MSAcpi_ThermalZoneTemperature | Select-Object -First 1; if($temp) { $celsius = [math]::Round(($temp.CurrentTemperature/10)-273.15,1); Write-Host \"Température CPU: $celsius°C\" -ForegroundColor $(if($celsius -gt 80){'Red'}elseif($celsius -gt 70){'Yellow'}else{'Green'}) } else { Write-Host 'Capteur de température non disponible' -ForegroundColor Red } } catch { Write-Host 'Erreur de lecture du capteur' -ForegroundColor Red }"
timeout /t 2 /nobreak >nul
GOTO TempLoop

:Retour
exit
