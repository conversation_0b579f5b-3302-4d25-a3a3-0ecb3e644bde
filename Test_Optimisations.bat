@echo off
title Test des Optimisations - MZEER Edition

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo ===============================================================================
echo                        TEST DES OPTIMISATIONS APPLIQUEES
echo ===============================================================================
echo.

echo Verification des optimisations principales...
echo.

echo [1] Network Throttling:
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295"
if %ERRORLEVEL% == 0 (
    echo [OK] Network Throttling DESACTIVE - Optimisation OK
) else (
    echo [X] Network Throttling actif - Optimisation manquante
)

echo.
echo [2] CPU Scheduling:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26"
if %ERRORLEVEL% == 0 (
    echo [OK] CPU Scheduling OPTIMISE - Gaming Priority OK
) else (
    echo [X] CPU Scheduling par defaut - Optimisation manquante
)

echo.
echo [3] Memory Paging Executive:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Paging Executive DESACTIVE - Performance OK
) else (
    echo [X] Paging Executive actif - Optimisation manquante
)

echo.
echo [4] Timer Resolution:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Timer Resolution ULTRA active - Latence minimale
) else (
    echo [X] Timer Resolution par defaut - Optimisation manquante
)

echo.
echo [5] Game DVR:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] Game DVR DESACTIVE - FPS maximaux
) else (
    echo [X] Game DVR actif - Impact sur FPS possible
)

echo.
echo [6] Hardware GPU Scheduling:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2"
if %ERRORLEVEL% == 0 (
    echo [OK] Hardware GPU Scheduling ACTIVE - GPU optimise
) else (
    echo [X] Hardware GPU Scheduling inactif - Optimisation manquante
)

echo.
echo ===============================================================================
echo                              RESUME FINAL                                   
echo ===============================================================================

rem Comptage des optimisations
set /a count=0

reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" 2>nul | findstr /i "0x1" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1" >nul && set /a count+=1
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2" >nul && set /a count+=1

echo.
echo OPTIMISATIONS DETECTEES: %count%/6
echo.

if %count% == 6 (
    echo [PARFAIT] Toutes les optimisations sont actives!
    echo Votre systeme est COMPLETEMENT optimise pour le gaming!
    echo.
    echo PERFORMANCES ATTENDUES:
    echo + FPS: +40%% a +80%%
    echo + Input Lag: -80%% a -95%%
    echo + Latence Reseau: -70%%
    echo + Micro-stuttering: -95%%
    echo + Reactivite: +500%%
) else if %count% GEQ 4 (
    echo [BIEN] La plupart des optimisations sont actives.
    echo Votre systeme est bien optimise.
    echo.
    echo Si certaines optimisations manquent, un redemarrage
    echo peut etre necessaire pour les activer.
) else if %count% GEQ 2 (
    echo [MOYEN] Quelques optimisations sont actives.
    echo Certaines optimisations peuvent necessiter:
    echo + Un redemarrage du systeme
    echo + Des droits administrateur
) else (
    echo [ATTENTION] Peu d'optimisations detectees.
    echo.
    echo SOLUTIONS POSSIBLES:
    echo + Executer l'optimisation en tant qu'administrateur
    echo + Redemarrer le systeme apres optimisation
    echo + Verifier que l'antivirus n'interfere pas
)

echo.
echo ===============================================================================
echo.
echo Merci d'avoir utilise MZEER Edition!
echo Pour plus d'optimisations: https://www.twitch.tv/mzeer_
echo.
pause
