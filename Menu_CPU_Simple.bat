@echo off
CLS

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script necessite des droits administratifs. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations CPU Avance - MZEER Edition

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo ===============================================================================
echo                          OPTIMISATIONS CPU AVANCE                           
echo                               MZEER EDITION                                    
echo ===============================================================================
echo.

echo -------------------------------------------------------------------------------
echo                            MENU CPU AVANCE                                 
echo -------------------------------------------------------------------------------
echo   1. Planificateur CPU            6. Analyse CPU                          
echo   2. Core Parking                 7. Optimisation Complete               
echo   3. Classes de Priorite          8. Monitoring Temperature              
echo   4. Affinite Interruptions       9. Restaurer CPU                       
echo   5. Gestion Energie CPU          0. Retour Menu Principal               
echo -------------------------------------------------------------------------------
echo.

CHOICE /C 1234567890 /M "Selectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO RestaurerCPU
IF ERRORLEVEL 8 GOTO MonitoringTemperature
IF ERRORLEVEL 7 GOTO OptimisationComplete
IF ERRORLEVEL 6 GOTO AnalyseCPU
IF ERRORLEVEL 5 GOTO GestionEnergie
IF ERRORLEVEL 4 GOTO AffiniteInterruptions
IF ERRORLEVEL 3 GOTO ClassesPriorite
IF ERRORLEVEL 2 GOTO CoreParking
IF ERRORLEVEL 1 GOTO PlanificateurCPU

:PlanificateurCPU
CLS
title Planificateur CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION PLANIFICATEUR CPU
echo.
echo Cette optimisation va:
echo • Optimiser le planificateur de processus Windows
echo • Ameliorer la repartition des taches sur les coeurs CPU
echo • Reduire la latence de commutation des threads
echo • Optimiser les priorites de planification
echo.

choice /c:yn /n /m "Optimiser le planificateur CPU? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Optimisation du planificateur CPU...

rem CPU Scheduling optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" /t REG_DWORD /d 38 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "DpcWatchdogProfileOffset" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "DpcTimeout" /t REG_DWORD /d 0 /f >nul 2>&1

echo.
echo ✓ Planificateur CPU optimise!
echo   Gain attendu: +15%% reactivite systeme
echo.
pause
GOTO Menu

:CoreParking
CLS
title Core Parking

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo CONFIGURATION CORE PARKING
echo.
echo Choisissez votre configuration:
echo.
echo 1. Gaming (Desactiver Core Parking)
echo 2. Performance (Core Parking Minimal)
echo 3. Equilibre (Core Parking Modere)
echo 4. Economie d'energie (Core Parking Actif)
echo 5. Retour au menu
echo.

CHOICE /C 12345 /M "Selectionnez votre configuration:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO CoreParkingEco
IF ERRORLEVEL 3 GOTO CoreParkingBalanced
IF ERRORLEVEL 2 GOTO CoreParkingPerformance
IF ERRORLEVEL 1 GOTO CoreParkingGaming

:CoreParkingGaming
echo.
echo Configuration Core Parking Gaming (Desactive)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Core Parking Gaming configure (Desactive)!
GOTO CoreParkingComplete

:CoreParkingPerformance
echo.
echo Configuration Core Parking Performance (Minimal)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 1 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 25 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Core Parking Performance configure!
GOTO CoreParkingComplete

:CoreParkingBalanced
echo.
echo Configuration Core Parking Equilibre...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 50 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Core Parking Equilibre configure!
GOTO CoreParkingComplete

:CoreParkingEco
echo.
echo Configuration Core Parking Economie d'energie...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 3 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 75 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Core Parking Economie configure!
GOTO CoreParkingComplete

:CoreParkingComplete
echo.
echo Configuration Core Parking appliquee!
echo.
pause
GOTO Menu

:ClassesPriorite
CLS
title Classes de Priorite CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo CONFIGURATION CLASSES DE PRIORITE CPU
echo.
echo Cette optimisation va:
echo • Configurer les classes de priorite CPU
echo • Optimiser la repartition des ressources processeur
echo • Ameliorer les performances des applications critiques
echo • Reduire les conflits de priorite
echo.

choice /c:yn /n /m "Configurer les classes de priorite CPU? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Configuration des classes de priorite CPU...

rem CPU Priority optimizations
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Affinity" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Background Only" /t REG_SZ /d "False" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" /t REG_DWORD /d 6 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Scheduling Category" /t REG_SZ /d "High" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "SFIO Priority" /t REG_SZ /d "High" /f >nul 2>&1

echo.
echo ✓ Classes de priorite CPU configurees!
echo   Gain attendu: Priorite maximale pour les jeux
echo.
pause
GOTO Menu

:AffiniteInterruptions
CLS
title Affinite Interruptions

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION AFFINITE INTERRUPTIONS
echo.
echo Cette optimisation va:
echo • Optimiser l'affinite des interruptions
echo • Repartir les interruptions sur les coeurs CPU
echo • Reduire la latence des interruptions
echo • Ameliorer les performances gaming
echo.

choice /c:yn /n /m "Optimiser l'affinite des interruptions? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Optimisation de l'affinite des interruptions...

rem Interrupt affinity optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "DpcWatchdogProfileOffset" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "InterruptSteeringDisabled" /t REG_DWORD /d 0 /f >nul 2>&1

rem Optimize interrupt handling
powershell -Command "try { Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object { Set-NetAdapterAdvancedProperty -Name $_.Name -DisplayName 'Interrupt Moderation' -DisplayValue 'Disabled' -ErrorAction SilentlyContinue } } catch { }" >nul 2>&1

echo.
echo ✓ Affinite des interruptions optimisee!
echo   Gain attendu: -20%% latence interruptions
echo.
pause
GOTO Menu

:GestionEnergie
CLS
title Gestion Energie CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo GESTION ENERGIE CPU
echo.
echo Choisissez votre profil:
echo.
echo 1. Gaming Ultra (Performance Maximale)
echo 2. Haute Performance
echo 3. Equilibre
echo 4. Economie d'energie
echo 5. Retour au menu
echo.

CHOICE /C 12345 /M "Selectionnez votre profil:"
IF ERRORLEVEL 5 GOTO Menu
IF ERRORLEVEL 4 GOTO PowerEco
IF ERRORLEVEL 3 GOTO PowerBalanced
IF ERRORLEVEL 2 GOTO PowerHigh
IF ERRORLEVEL 1 GOTO PowerGaming

:PowerGaming
echo.
echo Configuration Gaming Ultra (Performance Maximale)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Profil Gaming Ultra configure!
GOTO PowerComplete

:PowerHigh
echo.
echo Configuration Haute Performance...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 90 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 90 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Profil Haute Performance configure!
GOTO PowerComplete

:PowerBalanced
echo.
echo Configuration Equilibree...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 94 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 75 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 5 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 5 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Profil Equilibre configure!
GOTO PowerComplete

:PowerEco
echo.
echo Configuration Economie d'energie...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 50 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 50 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 893dee8e-2bef-41e0-89c6-b55d0929964c 0 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
echo ✓ Profil Economie configure!
GOTO PowerComplete

:PowerComplete
echo.
echo Configuration de gestion d'energie CPU appliquee!
echo.
pause
GOTO Menu

:AnalyseCPU
CLS
title Analyse CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo ANALYSE DES PERFORMANCES CPU
echo.
echo Analyse en cours...
echo.

echo Informations CPU:
powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed, CurrentClockSpeed, LoadPercentage | Format-List"

echo.
echo Utilisation CPU par processus:
powershell -Command "Get-Process | Sort-Object CPU -Descending | Select-Object -First 10 Name, @{Name='CPU (%)';Expression={[math]::Round($_.CPU,2)}}, @{Name='Memoire (MB)';Expression={[math]::Round($_.WorkingSet/1MB,2)}} | Format-Table -AutoSize"

echo.
pause
GOTO Menu

:OptimisationComplete
CLS
title Optimisation Complete CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION COMPLETE CPU
echo.
echo Cette option applique TOUTES les optimisations CPU:
echo • Planificateur CPU
echo • Core Parking Gaming (Desactive)
echo • Classes de Priorite
echo • Affinite Interruptions
echo • Gestion Energie Gaming Ultra
echo.

choice /c:yn /n /m "Appliquer toutes les optimisations CPU? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo ========================================
echo APPLICATION DE TOUTES LES OPTIMISATIONS
echo ========================================

echo.
echo [1/5] Planificateur CPU...
call :PlanificateurSilent

echo [2/5] Core Parking Gaming...
call :CoreParkingGamingSilent

echo [3/5] Classes de Priorite...
call :ClassesPrioriteSilent

echo [4/5] Affinite Interruptions...
call :AffiniteInterruptionsSilent

echo [5/5] Gestion Energie Gaming...
call :PowerGamingSilent

echo.
echo ========================================
echo OPTIMISATION COMPLETE TERMINEE!
echo ========================================
echo.
echo Gains attendus:
echo • Performances CPU: +25%%
echo • Reactivite: +30%%
echo • Latence: -40%%
echo • Gaming: Priorite maximale
echo.
echo REDEMARRAGE RECOMMANDE pour appliquer tous les changements.
echo.
choice /c:yn /n /m "Redemarrer maintenant? [Y]es/[N]o: "
if %ERRORLEVEL% == 1 shutdown /r /t 10 /c "Redemarrage pour optimisations CPU - MZEER Edition"

GOTO Menu

:MonitoringTemperature
CLS
title Monitoring Temperature CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo MONITORING TEMPERATURE CPU
echo.
echo Surveillance de la temperature CPU...
echo Appuyez sur Ctrl+C pour arreter.
echo.

:TempLoop
powershell -Command "try { $temp = Get-WmiObject -Namespace 'root\wmi' -Class MSAcpi_ThermalZoneTemperature | Select-Object -First 1; if($temp) { $celsius = [math]::Round(($temp.CurrentTemperature/10)-273.15,1); Write-Host \"Temperature CPU: $celsius°C\" -ForegroundColor $(if($celsius -gt 80){'Red'}elseif($celsius -gt 70){'Yellow'}else{'Green'}) } else { Write-Host 'Capteur de temperature non disponible' -ForegroundColor Red } } catch { Write-Host 'Erreur de lecture du capteur' -ForegroundColor Red }"
timeout /t 2 /nobreak >nul
GOTO TempLoop

:RestaurerCPU
CLS
title Restaurer CPU

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo RESTAURATION PARAMETRES CPU
echo.
echo Cette action va restaurer tous les parametres CPU par defaut.
echo.

choice /c:yn /n /m "Restaurer les parametres CPU? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Restauration en cours...

rem Restore default CPU settings
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 2 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 94 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 75 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1

rem Reset registry values
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" /t REG_DWORD /d 2 /f >nul 2>&1

echo.
echo ✓ Parametres CPU restaures par defaut.
echo   Un redemarrage est recommande.
echo.
pause
GOTO Menu

rem Silent functions
:PlanificateurSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" /t REG_DWORD /d 38 /f >nul 2>&1
exit /b

:CoreParkingGamingSilent
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
exit /b

:ClassesPrioriteSilent
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" /t REG_DWORD /d 6 /f >nul 2>&1
exit /b

:AffiniteInterruptionsSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "DpcWatchdogProfileOffset" /t REG_DWORD /d 1 /f >nul 2>&1
exit /b

:PowerGamingSilent
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1
exit /b

:Retour
exit
