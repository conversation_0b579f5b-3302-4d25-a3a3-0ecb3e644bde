@echo off
title Test PowerShell Errors

echo ========================================
echo TEST DES COMMANDES POWERSHELL
echo ========================================
echo.

echo Test 1: Verification PowerShell...
powershell -Command "Write-Host 'PowerShell fonctionne correctement'" 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERREUR: PowerShell ne fonctionne pas correctement
    pause
    exit /b 1
)

echo.
echo Test 2: Test de lancement d'un fichier simple...
echo Tentative de lancement du menu reseau avec PowerShell...
echo.

powershell -Command "Write-Host 'Tentative de lancement...'; try { Start-Process 'Opti Détaillés\\Optimisations Avancées\\Réseau & Latence\\Menu Réseau.bat' -ErrorAction Stop; Write-Host 'Lancement reussi!' } catch { Write-Host 'ERREUR:' $_.Exception.Message -ForegroundColor Red }" 2>&1

echo.
echo Test 3: Verification des chemins avec PowerShell...
powershell -Command "if (Test-Path 'Opti Détaillés\\Optimisations Avancées\\Réseau & Latence\\Menu Réseau.bat') { Write-Host 'Fichier Menu Reseau TROUVE' -ForegroundColor Green } else { Write-Host 'Fichier Menu Reseau INTROUVABLE' -ForegroundColor Red }" 2>&1

echo.
echo Test 4: Test avec chemin absolu...
powershell -Command "$path = Join-Path (Get-Location) 'Opti Détaillés\\Optimisations Avancées\\Réseau & Latence\\Menu Réseau.bat'; Write-Host 'Chemin complet:' $path; if (Test-Path $path) { Write-Host 'Fichier TROUVE avec chemin absolu' -ForegroundColor Green } else { Write-Host 'Fichier INTROUVABLE avec chemin absolu' -ForegroundColor Red }" 2>&1

echo.
echo ========================================
echo FIN DES TESTS POWERSHELL
echo ========================================
pause
