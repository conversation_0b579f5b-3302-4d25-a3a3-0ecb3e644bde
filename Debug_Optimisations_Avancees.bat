@echo off
title Debug - Optimisations Avancees

echo ========================================
echo DEBUG - OPTIMISATIONS AVANCEES
echo ========================================
echo.

echo 1. Test d'existence du fichier menu principal...
if exist "Menu_Optimisations_Avancees.bat" (
    echo ✓ Menu_Optimisations_Avancees.bat TROUVE
) else (
    echo ✗ Menu_Optimisations_Avancees.bat INTROUVABLE
    echo ERREUR: Le fichier menu principal n'existe pas!
    pause
    exit /b 1
)

echo.
echo 2. Test des droits administrateur...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ✗ DROITS ADMINISTRATEUR REQUIS
    echo Redemarrage avec droits admin...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
) else (
    echo ✓ DROITS ADMINISTRATEUR OK
)

echo.
echo 3. Test d'existence des sous-menus...

if exist "Opti Détaillés\Optimisations Avancées\Réseau & Latence\Menu Réseau.bat" (
    echo ✓ Menu Reseau TROUVE
) else (
    echo ✗ Menu Reseau INTROUVABLE
)

if exist "Opti Détaillés\Optimisations Avancées\Mémoire Avancée\Menu Mémoire.bat" (
    echo ✓ Menu Memoire TROUVE
) else (
    echo ✗ Menu Memoire INTROUVABLE
)

if exist "Opti Détaillés\Optimisations Avancées\CPU Avancé\Menu CPU.bat" (
    echo ✓ Menu CPU TROUVE
) else (
    echo ✗ Menu CPU INTROUVABLE
)

if exist "Opti Détaillés\Optimisations Avancées\Gaming Ultra Performance\Menu Gaming Ultra.bat" (
    echo ✓ Menu Gaming Ultra TROUVE
) else (
    echo ✗ Menu Gaming Ultra INTROUVABLE
)

echo.
echo 4. Test de lancement du menu principal...
echo Tentative de lancement en mode debug...
echo.

echo ========================================
echo LANCEMENT DU MENU PRINCIPAL
echo ========================================
echo.

rem Lancement avec capture d'erreur
call "Menu_Optimisations_Avancees.bat" 2>&1

echo.
echo ========================================
echo FIN DU DEBUG
echo ========================================
echo.
echo Si une erreur s'est produite, elle devrait etre visible ci-dessus.
echo.
pause
