# Script pour remplacer toutes les références MathysM par MZEER
# Replace all MathysM references with MZEER

Write-Host "🔄 Mise à jour du branding MathysM vers MZEER..." -ForegroundColor Yellow

try {
    # Définir les chemins de recherche
    $searchPaths = @(
        "Opti Détaillés\Optimisations Avancées\",
        "Ressources\Gaming Ultra\",
        "Ressources\Mémoire\",
        "Ressources\Réseau\",
        "Ressources\CPU\"
    )
    
    # Extensions de fichiers à traiter
    $fileExtensions = @("*.bat", "*.ps1", "*.reg")
    
    $totalFiles = 0
    $updatedFiles = 0
    
    foreach ($searchPath in $searchPaths) {
        if (Test-Path $searchPath) {
            Write-Host "📁 Traitement du dossier: $searchPath" -ForegroundColor Cyan
            
            foreach ($extension in $fileExtensions) {
                $files = Get-ChildItem -Path $searchPath -Filter $extension -Recurse -ErrorAction SilentlyContinue
                
                foreach ($file in $files) {
                    $totalFiles++
                    $content = Get-Content -Path $file.FullName -Raw -ErrorAction SilentlyContinue
                    
                    if ($content -and ($content -match "MathysM" -or $content -match "beacons\.ai/mathysm")) {
                        Write-Host "  🔧 Mise à jour: $($file.Name)" -ForegroundColor Green
                        
                        # Remplacer MathysM par MZEER
                        $content = $content -replace "MathysM", "MZEER"
                        
                        # Remplacer les liens beacons.ai par twitch
                        $content = $content -replace "beacons\.ai/mathysm", "https://www.twitch.tv/mzeer_"
                        $content = $content -replace "beacons\.ai\/mathysm", "https://www.twitch.tv/mzeer_"
                        
                        # Remplacer les descriptions de points de restauration
                        $content = $content -replace "Avant.*MathysM", "Avant Optimisation - MZEER"
                        $content = $content -replace "Ultra Gaming Profile - MathysM", "Ultra Gaming Profile - MZEER"
                        $content = $content -replace "Avant Auto Opti Lite - MZEER", "Avant Auto Opti Lite - MZEER"
                        
                        # Sauvegarder le fichier modifié
                        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
                        $updatedFiles++
                    }
                }
            }
        }
    }
    
    Write-Host "`n📊 RÉSULTATS:" -ForegroundColor Yellow
    Write-Host "• Fichiers analysés: $totalFiles" -ForegroundColor White
    Write-Host "• Fichiers mis à jour: $updatedFiles" -ForegroundColor White
    
    if ($updatedFiles -gt 0) {
        Write-Host "`n✅ Branding mis à jour avec succès!" -ForegroundColor Green
        Write-Host "• MathysM → MZEER" -ForegroundColor White
        Write-Host "• beacons.ai/mathysm → https://www.twitch.tv/mzeer_" -ForegroundColor White
    } else {
        Write-Host "`n✅ Aucune mise à jour nécessaire" -ForegroundColor Green
    }
    
} catch {
    Write-Host "`n❌ Erreur lors de la mise à jour: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAppuyez sur une touche pour continuer..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
