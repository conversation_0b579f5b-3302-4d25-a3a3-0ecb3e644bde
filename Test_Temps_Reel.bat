@echo off

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Droits administrateur requis. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

title Test Temps Reel - MZEER Edition

echo.
echo ===============================================================================
echo                    TEST TEMPS REEL - MZEER EDITION                          
echo ===============================================================================
echo.
echo Ce script teste chaque commande et verifie IMMEDIATEMENT si elle fonctionne
echo.

echo ========================================
echo TEST 1: NETWORK THROTTLING
echo ========================================

echo AVANT:
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul

echo.
echo EXECUTION DE LA COMMANDE:
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" /t REG_DWORD /d 4294967295 /f

echo.
echo VERIFICATION IMMEDIATE:
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Network Throttling bien configure!
) else (
    echo [ECHEC] Network Throttling non configure!
)

echo.
echo ========================================
echo TEST 2: CPU SCHEDULING
echo ========================================

echo AVANT:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul

echo.
echo EXECUTION DE LA COMMANDE:
reg add "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" /t REG_DWORD /d 38 /f

echo.
echo VERIFICATION IMMEDIATE:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] CPU Scheduling bien configure!
) else (
    echo [ECHEC] CPU Scheduling non configure!
)

echo.
echo ========================================
echo TEST 3: TIMER RESOLUTION
echo ========================================

echo AVANT:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul

echo.
echo EXECUTION DE LA COMMANDE:
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" /t REG_DWORD /d 1 /f

echo.
echo VERIFICATION IMMEDIATE:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Timer Resolution bien configure!
) else (
    echo [ECHEC] Timer Resolution non configure!
)

echo.
echo ========================================
echo TEST 4: GAME DVR
echo ========================================

echo AVANT:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul

echo.
echo EXECUTION DE LA COMMANDE:
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" /t REG_DWORD /d 0 /f

echo.
echo VERIFICATION IMMEDIATE:
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Game DVR bien desactive!
) else (
    echo [ECHEC] Game DVR non desactive!
)

echo.
echo ========================================
echo TEST 5: HARDWARE GPU SCHEDULING
echo ========================================

echo AVANT:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul

echo.
echo EXECUTION DE LA COMMANDE:
reg add "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" /t REG_DWORD /d 2 /f

echo.
echo VERIFICATION IMMEDIATE:
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Hardware GPU Scheduling bien active!
) else (
    echo [ECHEC] Hardware GPU Scheduling non active!
)

echo.
echo ========================================
echo TEST 6: SERVICES (WINDOWS SEARCH)
echo ========================================

echo AVANT:
sc qc "WSearch" | findstr /i "START_TYPE"

echo.
echo EXECUTION DE LA COMMANDE:
sc config "WSearch" start= disabled

echo.
echo VERIFICATION IMMEDIATE:
sc qc "WSearch" | findstr /i "DISABLED"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Windows Search bien desactive!
) else (
    echo [ECHEC] Windows Search non desactive!
)

echo.
echo ========================================
echo TEST 7: STOCKAGE (LAST ACCESS TIME)
echo ========================================

echo AVANT:
fsutil behavior query DisableLastAccess

echo.
echo EXECUTION DE LA COMMANDE:
fsutil behavior set DisableLastAccess 1

echo.
echo VERIFICATION IMMEDIATE:
fsutil behavior query DisableLastAccess | findstr /i "DisableLastAccess = 1"
if %ERRORLEVEL% == 0 (
    echo [SUCCES] Last Access Time bien desactive!
) else (
    echo [ECHEC] Last Access Time non desactive!
)

echo.
echo ========================================
echo TEST 8: NETTOYAGE REEL
echo ========================================

echo Creation de fichiers test...
echo test > "%temp%\test_mzeer_1.tmp"
echo test > "%temp%\test_mzeer_2.tmp"
echo test > "%temp%\test_mzeer_3.tmp"

echo AVANT nettoyage:
if exist "%temp%\test_mzeer_*.tmp" (
    echo [INFO] Fichiers test presents
) else (
    echo [INFO] Aucun fichier test
)

echo.
echo EXECUTION DU NETTOYAGE:
for /f "delims=" %%i in ('dir /b "%temp%\test_mzeer_*.tmp" 2^>nul') do (
    del /f /q "%temp%\%%i" >nul 2>&1
)

echo.
echo VERIFICATION IMMEDIATE:
if exist "%temp%\test_mzeer_*.tmp" (
    echo [ECHEC] Fichiers test non supprimes!
) else (
    echo [SUCCES] Fichiers test bien supprimes!
)

echo.
echo ===============================================================================
echo                              RESUME FINAL                                   
echo ===============================================================================
echo.

echo TOUS LES TESTS TEMPS REEL TERMINES!
echo.
echo RESULTATS:
echo - Chaque commande a ete testee individuellement
echo - Verification immediate apres execution
echo - Vous pouvez voir exactement ce qui fonctionne ou non
echo.
echo RECOMMANDATION:
echo Si tous les tests montrent [SUCCES], l'optimiseur fonctionne parfaitement!
echo Si certains montrent [ECHEC], il peut y avoir un probleme de droits ou de compatibilite.
echo.
echo Pour une verification complete, utilisez: Verification_Complete_100.bat
echo.
echo ===============================================================================
echo.
pause
