@echo off
chcp 65001
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Auto Opti Lite - Menu

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███╗   ███╗███████╗███╗   ██╗██╗   ██╗
echo       ████╗ ████║██╔════╝████╗  ██║██║   ██║
echo       ██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║
echo       ██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║
echo       ██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝
echo       ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝ 
echo.
echo.

echo 1. Auto Opti Lite
echo 2. Remettre par défaut
echo 3. Optimisations avancées
echo 4. Soutenir le projet
echo.

CHOICE /C 1234 /M "Entre ton choix:"
IF ERRORLEVEL 4 GOTO Soutenir le projet
IF ERRORLEVEL 3 GOTO OptiAvancees
IF ERRORLEVEL 2 GOTO Default
IF ERRORLEVEL 1 GOTO Prévention

:Explication
CLS

title Auto Opti Lite

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███████╗██╗  ██╗██████╗ ██╗     ██╗ ██████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
echo       ██╔════╝╚██╗██╔╝██╔══██╗██║     ██║██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
echo       █████╗   ╚███╔╝ ██████╔╝██║     ██║██║     ███████║   ██║   ██║██║   ██║██╔██╗ ██║
echo       ██╔══╝   ██╔██╗ ██╔═══╝ ██║     ██║██║     ██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
echo       ███████╗██╔╝ ██╗██║     ███████╗██║╚██████╗██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
echo       ╚══════╝╚═╝  ╚═╝╚═╝     ╚══════╝╚═╝ ╚═════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
echo.                                                                                  
echo.

start "" "https://youtu.be/wih5pHbadgs"

echo.
echo.
echo Retour menu dans:
for /L %%i in (4,-1,1) do (
    echo %%i...
    timeout /t 1 /nobreak >nul
)
GOTO Menu

:Prévention
CLS

title Auto Opti Lite - Important

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ██╗███╗   ███╗██████╗  ██████╗ ██████╗ ████████╗ █████╗ ███╗   ██╗████████╗
echo       ██║████╗ ████║██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝
echo       ██║██╔████╔██║██████╔╝██║   ██║██████╔╝   ██║   ███████║██╔██╗ ██║   ██║   
echo       ██║██║╚██╔╝██║██╔═══╝ ██║   ██║██╔══██╗   ██║   ██╔══██║██║╚██╗██║   ██║   
echo       ██║██║ ╚═╝ ██║██║     ╚██████╔╝██║  ██║   ██║   ██║  ██║██║ ╚████║   ██║   
echo       ╚═╝╚═╝     ╚═╝╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   
echo.
echo.

echo -------------------------------------------------------------------------------
echo - Merci d'utiliser un ISO officiel et fraîchement installé (via clé bootable) -
echo -------------------------------------------------------------------------------
echo Pour remettre par défaut, merci d'utiliser la fonction "Remettre par défaut"
echo.
echo.

choice /c:yn /n /m "Ton Windows est-il fraîchement installé? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO OptiLite
if %ERRORLEVEL% == 2 GOTO CleanInstall

:CleanInstall

start "" "https://youtu.be/lgTH2kwOA_A"

GOTO Quitter

:OptiLite

call :Save

rem Point de Restauration
net start vss

start powershell -Command "Enable-ComputerRestore -Drive C:"
VSSADMIN Resize Shadowstorage /For=C: /On=C: /MaxSize=4%%

REG ADD "HKEY_LOCAL_MACHINE\SOFTWARE\Policies\Microsoft\Windows NT\SystemRestore" /v "DisableSR" /t REG_DWORD /d 0 /f
REG ADD "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SystemRestore" /v "SystemRestorePointCreationFrequency" /t REG_DWORD /d "0" /f

call :Save

rem Lancement du point de restauration et fermeture automatique de la fenêtre PowerShell
start /wait powershell -Command "Checkpoint-Computer -Description 'Avant Auto Opti Lite - MZEER' -RestorePointType 'MODIFY_SETTINGS'"

call :Save

echo ----------------------------------------------------------------
echo - Point de restauration nommé "Avant Auto Opti Lite - MZEER" -
echo ----------------------------------------------------------------
echo.
echo Ne ferme pas le script, des écrans noirs peuvent apparaitre

CLS

title Abonne-toi!

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███████╗██╗   ██╗██████╗ ███████╗ ██████╗██████╗ ██╗██████╗ ███████╗
echo       ██╔════╝██║   ██║██╔══██╗██╔════╝██╔════╝██╔══██╗██║██╔══██╗██╔════╝
echo       ███████╗██║   ██║██████╔╝███████╗██║     ██████╔╝██║██████╔╝█████╗  
echo       ╚════██║██║   ██║██╔══██╗╚════██║██║     ██╔══██╗██║██╔══██╗██╔══╝  
echo       ███████║╚██████╔╝██████╔╝███████║╚██████╗██║  ██║██║██████╔╝███████╗
echo       ╚══════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝╚═╝  ╚═╝╚═╝╚═════╝ ╚══════╝
echo.
echo.

timeout /t 4 /nobreak >nul
start "" "%~dp0\Ressources\Youtube.url"
timeout /t 4 /nobreak >nul

CLS
call "%~dp0\Ressources\Auto Opti\Auto Opti Lite.bat"
CLS

title Boost FPS

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ██╗     ███████╗███████╗ ██████╗ 
echo       ██║     ██╔════╝██╔════╝██╔════╝ 
echo       ██║     ███████╗█████╗  ██║  ███╗
echo       ██║     ╚════██║██╔══╝  ██║   ██║
echo       ███████╗███████║██║     ╚██████╔╝
echo       ╚══════╝╚══════╝╚═╝      ╚═════╝ 
echo.
echo.                            

choice /c:yn /n /m "Veux-tu doubler ou même tripler tes FPS (no fake)? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO LSFG
if %ERRORLEVEL% == 2 GOTO SkipLSFG

:LSFG

start "" "https://youtu.be/OSwwR5WIikc"

GOTO SkipLSFG

:SkipLSFG
CLS

title Optimisation terminé!

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███╗   ███╗███████╗██████╗  ██████╗██╗
echo       ████╗ ████║██╔════╝██╔══██╗██╔════╝██║
echo       ██╔████╔██║█████╗  ██████╔╝██║     ██║
echo       ██║╚██╔╝██║██╔══╝  ██╔══██╗██║     ██║
echo       ██║ ╚═╝ ██║███████╗██║  ██║╚██████╗██║
echo       ╚═╝     ╚═╝╚══════╝╚═╝  ╚═╝ ╚═════╝╚═╝
echo.
echo.

echo --------------------------------------------------------
echo - N'hésite pas à faire un don pour soutenir le projet! -
echo --------------------------------------------------------
timeout /t 3 /nobreak >nul
start "" "%~dp0\Ressources\Ko-Fi.url"

echo.
echo.
choice /c:yn /n /m "Veux tu redémarrer pour appliquer les modifications? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO Reboot
if %ERRORLEVEL% == 2 GOTO Quitter

:Soutenir le projet
CLS

title Soutenir le projet

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███████╗ ██████╗ ██╗   ██╗████████╗███████╗███╗   ██╗██╗██████╗ 
echo       ██╔════╝██╔═══██╗██║   ██║╚══██╔══╝██╔════╝████╗  ██║██║██╔══██╗
echo       ███████╗██║   ██║██║   ██║   ██║   █████╗  ██╔██╗ ██║██║██████╔╝
echo       ╚════██║██║   ██║██║   ██║   ██║   ██╔══╝  ██║╚██╗██║██║██╔══██╗
echo       ███████║╚██████╔╝╚██████╔╝   ██║   ███████╗██║ ╚████║██║██║  ██║
echo       ╚══════╝ ╚═════╝  ╚═════╝    ╚═╝   ╚══════╝╚═╝  ╚═══╝╚═╝╚═╝  ╚═╝
echo.
echo.

start "" "https://www.twitch.tv/mzeer_"

timeout /t 6 /nobreak >nul

GOTO Menu

:OptiAvancees

call :ByeBye

start "" "%~dp0\Opti Détaillés\Optimisations Avancées\Menu Principal.bat"

call :ByeBye

GOTO Menu

:Default

call :ByeBye

call "%~dp0\Ressources\Auto Opti\Auto Opti Lite - Defaut.bat"

call :ByeBye

choice /c:yn /n /m "Veux tu redémarrer et appliquer les modifications? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO Reboot
if %ERRORLEVEL% == 2 GOTO Quitter

:Reboot

shutdown /r /t 60 /f /d p:0:0 /c "Merci d'avoir utilisé mon dossier"

GOTO Quitter

:Save
CLS

title Auto Opti Lite - Save

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ███████╗ █████╗ ██╗   ██╗███████╗
echo       ██╔════╝██╔══██╗██║   ██║██╔════╝
echo       ███████╗███████║██║   ██║█████╗  
echo       ╚════██║██╔══██║╚██╗ ██╔╝██╔══╝  
echo       ███████║██║  ██║ ╚████╔╝ ███████╗
echo       ╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝
echo. 
echo.
exit /b

:ByeBye
CLS

title Auto Opti Lite - Par défaut

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo       ██████╗ ██╗   ██╗███████╗    ██████╗ ██╗   ██╗███████╗
echo       ██╔══██╗╚██╗ ██╔╝██╔════╝    ██╔══██╗╚██╗ ██╔╝██╔════╝
echo       ██████╔╝ ╚████╔╝ █████╗      ██████╔╝ ╚████╔╝ █████╗  
echo       ██╔══██╗  ╚██╔╝  ██╔══╝      ██╔══██╗  ╚██╔╝  ██╔══╝  
echo       ██████╔╝   ██║   ███████╗    ██████╔╝   ██║   ███████╗
echo       ╚═════╝    ╚═╝   ╚══════╝    ╚═════╝    ╚═╝   ╚══════╝
echo.
echo.
exit /b

:Quitter

echo.