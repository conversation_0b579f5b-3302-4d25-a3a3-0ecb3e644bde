@echo off
chcp 65001
set "base_path=%~dp0..\..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

title Optimisation Complète Avancée - En cours...

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo        ██████╗ ██████╗ ████████╗██╗███╗   ███╗██╗███████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
echo       ██╔═══██╗██╔══██╗╚══██╔══╝██║████╗ ████║██║██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
echo       ██║   ██║██████╔╝   ██║   ██║██╔████╔██║██║███████╗███████║   ██║   ██║██║   ██║██╔██╗ ██║
echo       ██║   ██║██╔═══╝    ██║   ██║██║╚██╔╝██║██║╚════██║██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
echo       ╚██████╔╝██║        ██║   ██║██║ ╚═╝ ██║██║███████║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
echo        ╚═════╝ ╚═╝        ╚═╝   ╚═╝╚═╝     ╚═╝╚═╝╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
echo.
echo                                    COMPLÈTE AVANCÉE
echo.

echo ⚠️  ATTENTION: Cette optimisation appliquera TOUTES les optimisations avancées
echo.
echo 🔧 Optimisations qui seront appliquées:
echo • Réseau & Latence (TCP/IP, DNS, Throttling, Buffers, Gaming Priority)
echo • Mémoire Avancée (Compression, Paging File, Standby List, Large Pages)
echo • CPU Avancé (Scheduling, Core Parking, Priority Classes, Interrupts)
echo • Gaming Avancé (GPU Scheduling, DirectX, Fullscreen, Game DVR)
echo • Stockage Avancé (NTFS, Defrag, Write Cache, Prefetch/Superfetch)
echo • Sécurité vs Performance (Defender, Real-time, SmartScreen, UAC)
echo.
echo ⏱️  Temps estimé: 5-10 minutes
echo 🔄 Redémarrage OBLIGATOIRE après optimisation
echo.

choice /c:yn /n /m "Êtes-vous sûr de vouloir continuer? [Y]es/[N]o"
if %ERRORLEVEL% == 2 exit

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo                            DÉMARRAGE DE L'OPTIMISATION
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

rem Point de restauration
echo [ÉTAPE 0/6] Création du point de restauration...
net start vss >nul 2>&1
powershell -Command "Enable-ComputerRestore -Drive C:" >nul 2>&1
powershell -Command "Checkpoint-Computer -Description 'Avant Optimisation Complète Avancée - MZEER' -RestorePointType 'MODIFY_SETTINGS'" >nul 2>&1
echo ✅ Point de restauration créé

echo.
echo [ÉTAPE 1/6] OPTIMISATIONS RÉSEAU & LATENCE
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • TCP/IP Stack Optimization...
regedit /s "%base_path%Ressources\Réseau\TCP-IP Optimization.reg" >nul 2>&1
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global netdma=enabled >nul 2>&1

echo • DNS Cloudflare Configuration...
netsh interface ip set dns "Ethernet" static ******* >nul 2>&1
netsh interface ip add dns "Ethernet" ******* index=2 >nul 2>&1
netsh interface ip set dns "Wi-Fi" static ******* >nul 2>&1
netsh interface ip add dns "Wi-Fi" ******* index=2 >nul 2>&1
ipconfig /flushdns >nul 2>&1

echo • Network Throttling Désactivation...
regedit /s "%base_path%Ressources\Réseau\Disable Network Throttling.reg" >nul 2>&1
netsh int tcp set global nonsackrttresiliency=disabled >nul 2>&1
netsh int tcp set global maxsynretransmissions=2 >nul 2>&1

echo • Network Buffer Optimization...
regedit /s "%base_path%Ressources\Réseau\Network Buffer Optimization.reg" >nul 2>&1
netsh int tcp set global ecncapability=enabled >nul 2>&1
netsh int tcp set global timestamps=enabled >nul 2>&1

echo • Gaming Network Priority...
regedit /s "%base_path%Ressources\Réseau\Gaming Network Priority.reg" >nul 2>&1
netsh int tcp set global dca=enabled >nul 2>&1

echo • Network Power Management Désactivation...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Réseau\Disable Network Power Management.ps1" >nul 2>&1

echo ✅ Optimisations Réseau & Latence terminées

echo.
echo [ÉTAPE 2/6] OPTIMISATIONS MÉMOIRE AVANCÉE
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • Memory Compression Optimization...
regedit /s "%base_path%Ressources\Mémoire\Memory Compression Optimization.reg" >nul 2>&1
powershell -Command "Enable-MMAgent -MemoryCompression" >nul 2>&1
powershell -Command "Set-MMAgent -MemoryCompression $true" >nul 2>&1

echo • Gaming Paging File Configuration...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Set Gaming Paging File.ps1" >nul 2>&1

echo • Memory Standby List Optimization...
regedit /s "%base_path%Ressources\Mémoire\Memory Standby List Optimization.reg" >nul 2>&1

echo • Large Page Support Activation...
regedit /s "%base_path%Ressources\Mémoire\Large Page Support.reg" >nul 2>&1
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Mémoire\Enable Large Pages.ps1" >nul 2>&1

echo • Advanced Memory Management...
regedit /s "%base_path%Ressources\Mémoire\Advanced Memory Management.reg" >nul 2>&1

echo ✅ Optimisations Mémoire Avancée terminées

echo.
echo [ÉTAPE 3/6] OPTIMISATIONS CPU AVANCÉ
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • CPU Scheduling Optimization...
regedit /s "%base_path%Ressources\CPU\CPU Scheduling Optimization.reg" >nul 2>&1

echo • Core Parking Gaming (Désactivé)...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 0 >nul 2>&1
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 ea062031-0e34-4ff1-9b6d-eb1059334028 0 >nul 2>&1

echo • CPU Priority Classes...
regedit /s "%base_path%Ressources\CPU\CPU Priority Classes.reg" >nul 2>&1

echo • Interrupt Affinity Optimization...
regedit /s "%base_path%Ressources\CPU\Interrupt Affinity.reg" >nul 2>&1

echo • CPU Power Management Performance...
powercfg -setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setdcvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec 100 >nul 2>&1
powercfg -setactive SCHEME_CURRENT >nul 2>&1

echo ✅ Optimisations CPU Avancé terminées

echo.
echo [ÉTAPE 4/6] OPTIMISATIONS GAMING AVANCÉ
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • GPU Scheduling Advanced...
regedit /s "%base_path%Ressources\Gaming\GPU Scheduling Advanced.reg" >nul 2>&1

echo • DirectX Optimizations...
regedit /s "%base_path%Ressources\Gaming\DirectX Optimizations.reg" >nul 2>&1

echo • Fullscreen Optimizations...
regedit /s "%base_path%Ressources\Gaming\Fullscreen Optimizations.reg" >nul 2>&1

echo • Game DVR Complete Removal...
regedit /s "%base_path%Ressources\Gaming\Remove Game DVR.reg" >nul 2>&1

echo • Gaming Priority System...
regedit /s "%base_path%Ressources\Gaming\Gaming Priority System.reg" >nul 2>&1

echo ✅ Optimisations Gaming Avancé terminées

echo.
echo [ÉTAPE 5/6] OPTIMISATIONS STOCKAGE AVANCÉ
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • NTFS Optimizations...
regedit /s "%base_path%Ressources\Stockage\NTFS Optimizations.reg" >nul 2>&1

echo • Write Cache Optimization...
regedit /s "%base_path%Ressources\Stockage\Write Cache Optimization.reg" >nul 2>&1

echo • Prefetch/Superfetch Advanced...
regedit /s "%base_path%Ressources\Stockage\Prefetch Superfetch Advanced.reg" >nul 2>&1

echo • TRIM Activation...
fsutil behavior set DisableDeleteNotify 0 >nul 2>&1

echo ✅ Optimisations Stockage Avancé terminées

echo.
echo [ÉTAPE 6/6] OPTIMISATIONS SÉCURITÉ vs PERFORMANCE
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo • Windows Defender Gaming Exclusions...
powershell -NoProfile -ExecutionPolicy Bypass -File "%base_path%Ressources\Sécurité\Configure Defender Gaming.ps1" >nul 2>&1

echo • Real-time Protection Tuning...
regedit /s "%base_path%Ressources\Sécurité\Real-time Protection Tuning.reg" >nul 2>&1

echo • SmartScreen Optimization...
regedit /s "%base_path%Ressources\Sécurité\SmartScreen Optimization.reg" >nul 2>&1

echo • UAC Optimization...
regedit /s "%base_path%Ressources\Sécurité\UAC Optimization.reg" >nul 2>&1

echo ✅ Optimisations Sécurité vs Performance terminées

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo                            OPTIMISATION TERMINÉE!
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🎉 TOUTES les optimisations avancées ont été appliquées avec succès!
echo.
echo 📋 Résumé des optimisations appliquées:
echo ✅ Réseau & Latence: TCP/IP, DNS Cloudflare, Anti-throttling, Buffers, Gaming Priority
echo ✅ Mémoire Avancée: Compression, Paging File Gaming, Standby List, Large Pages
echo ✅ CPU Avancé: Scheduling, Core Parking OFF, Priority Classes, Interrupts
echo ✅ Gaming Avancé: GPU Scheduling, DirectX, Fullscreen, Game DVR Removal
echo ✅ Stockage Avancé: NTFS, Write Cache, Prefetch/Superfetch, TRIM
echo ✅ Sécurité vs Performance: Defender Gaming, Real-time Tuning, SmartScreen, UAC
echo.
echo ⚠️  REDÉMARRAGE OBLIGATOIRE MAINTENANT!
echo.
echo 🔄 Voulez-vous redémarrer maintenant pour appliquer toutes les optimisations?
choice /c:yn /n /m "[Y]es/[N]o (redémarrer plus tard)"
if %ERRORLEVEL% == 1 shutdown /r /t 10 /c "Redémarrage pour appliquer les optimisations avancées..."

echo.
echo Merci d'avoir utilisé les Optimisations Avancées de MZEER!
echo.
pause
