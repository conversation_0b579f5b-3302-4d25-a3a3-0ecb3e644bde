@echo off
CLS

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script necessite des droits administratifs. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations <PERSON>seau et Latence - MZEER Edition

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo ===============================================================================
echo                        OPTIMISATIONS RESEAU ET LATENCE                      
echo                               MZEER EDITION                                    
echo ===============================================================================
echo.

echo -------------------------------------------------------------------------------
echo                            MENU RESEAU AVANCE                              
echo -------------------------------------------------------------------------------
echo   1. TCP/IP Stack Ultra           6. Optimisation Complete Reseau         
echo   2. DNS Ultra Rapide             7. Test Latence                         
echo   3. Desactiver Throttling        8. Informations Detaillees              
echo   4. Gestion Energie Reseau       9. Restaurer Parametres                 
echo   5. QoS Gaming                   0. Retour Menu Principal                
echo -------------------------------------------------------------------------------
echo.

CHOICE /C 1234567890 /M "Selectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO Restaurer
IF ERRORLEVEL 8 GOTO Informations
IF ERRORLEVEL 7 GOTO TestLatence
IF ERRORLEVEL 6 GOTO OptimisationComplete
IF ERRORLEVEL 5 GOTO QoSGaming
IF ERRORLEVEL 4 GOTO GestionEnergie
IF ERRORLEVEL 3 GOTO DesactiverThrottling
IF ERRORLEVEL 2 GOTO DNSUltra
IF ERRORLEVEL 1 GOTO TCPIPStack

:TCPIPStack
CLS
title TCP/IP Stack Ultra Optimization

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION TCP/IP STACK ULTRA
echo.
echo Cette optimisation va:
echo - Optimiser la pile TCP/IP pour reduire la latence
echo - Configurer les buffers reseau pour de meilleures performances
echo - Activer les optimisations TCP avancees
echo - Configurer les parametres de congestion TCP
echo.

choice /c:yn /n /m "Continuer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Application des optimisations TCP/IP...

rem TCP/IP Stack Optimizations
netsh int tcp set global autotuninglevel=normal
netsh int tcp set global chimney=enabled
netsh int tcp set global rss=enabled
netsh int tcp set global netdma=enabled
netsh int tcp set global dca=enabled
netsh int tcp set global ecncapability=enabled
netsh int tcp set global timestamps=enabled
netsh int tcp set global initialrto=2000
netsh int tcp set global rsc=enabled
netsh int tcp set global nonsackrttresiliency=disabled

rem Advanced TCP Settings
netsh int tcp set global maxsynretransmissions=2
netsh int tcp set global fastopen=enabled
netsh int tcp set global fastopenfallback=enabled
netsh int tcp set global hystart=enabled
netsh int tcp set global prr=enabled
netsh int tcp set global pacingprofile=off

rem Registry optimizations for TCP/IP
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TcpAckFrequency" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TCPNoDelay" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TcpDelAckTicks" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "MaxUserPort" /t REG_DWORD /d 65534 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TcpTimedWaitDelay" /t REG_DWORD /d 30 /f >nul 2>&1

echo.
echo ✓ Optimisations TCP/IP appliquees avec succes!
echo   Gain attendu: -30%% latence reseau
echo.
pause
GOTO Menu

:DNSUltra
CLS
title DNS Ultra Rapide

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo CONFIGURATION DNS ULTRA RAPIDE
echo.
echo Cette optimisation va configurer:
echo • DNS Cloudflare (*******) - Le plus rapide
echo • DNS Google (*******) - Backup
echo • Cache DNS optimise
echo • Resolution DNS acceleree
echo.

choice /c:yn /n /m "Continuer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Configuration DNS ultra rapide...

rem Configure Cloudflare DNS (fastest)
netsh interface ip set dns "Ethernet" static ******* >nul 2>&1
netsh interface ip add dns "Ethernet" ******* index=2 >nul 2>&1
netsh interface ip set dns "Wi-Fi" static ******* >nul 2>&1
netsh interface ip add dns "Wi-Fi" ******* index=2 >nul 2>&1

rem Flush and optimize DNS cache
ipconfig /flushdns >nul 2>&1
netsh int ip reset >nul 2>&1
netsh winsock reset >nul 2>&1

rem DNS Cache optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" /v "CacheHashTableBucketSize" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" /v "CacheHashTableSize" /t REG_DWORD /d 384 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" /v "MaxCacheEntryTtlLimit" /t REG_DWORD /d 64000 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Dnscache\Parameters" /v "MaxSOACacheEntryTtlLimit" /t REG_DWORD /d 301 /f >nul 2>&1

echo.
echo ✓ DNS ultra rapide configure!
echo   Serveurs: Cloudflare ******* + *******
echo   Gain attendu: -50%% temps resolution DNS
echo.
pause
GOTO Menu

:DesactiverThrottling
CLS
title Desactiver Throttling Reseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo DESACTIVATION THROTTLING RESEAU WINDOWS
echo.
echo Cette optimisation va:
echo • Desactiver la limitation automatique de bande passante
echo • Supprimer les restrictions QoS Windows
echo • Optimiser les buffers reseau
echo • Maximiser les performances reseau
echo.

choice /c:yn /n /m "Continuer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Desactivation du throttling reseau...

rem Disable network throttling
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" /t REG_DWORD /d 4294967295 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "SystemResponsiveness" /t REG_DWORD /d 0 /f >nul 2>&1

rem Disable QoS packet scheduler
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Psched" /v "NonBestEffortLimit" /t REG_DWORD /d 0 /f >nul 2>&1

rem Network adapter optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces" /v "TcpAckFrequency" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces" /v "TCPNoDelay" /t REG_DWORD /d 1 /f >nul 2>&1

rem Disable Nagle's algorithm for gaming
reg add "HKLM\SOFTWARE\Microsoft\MSMQ\Parameters" /v "TCPNoDelay" /t REG_DWORD /d 1 /f >nul 2>&1

echo.
echo ✓ Throttling reseau desactive!
echo   Gain attendu: +25%% debit reseau, -40%% latence
echo.
pause
GOTO Menu

:GestionEnergie
CLS
title Gestion Energie Reseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION GESTION ENERGIE RESEAU
echo.
echo Cette optimisation va:
echo • Desactiver l'economie d'energie des cartes reseau
echo • Forcer les performances maximales
echo • Empecher la mise en veille des adaptateurs
echo • Optimiser la gestion d'energie USB (pour dongles WiFi)
echo.

choice /c:yn /n /m "Continuer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Optimisation gestion energie reseau...

rem Disable power saving for network adapters
powershell -Command "Get-NetAdapter | ForEach-Object { $adapter = $_; try { $powerMgmt = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object { $_.NetConnectionID -eq $adapter.InterfaceAlias }; if ($powerMgmt) { $powerMgmt.SetPowerState(1) } } catch { } }" >nul 2>&1

rem Registry optimizations for network power management
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4D36E972-E325-11CE-BFC1-08002BE10318}\0001" /v "PnPCapabilities" /t REG_DWORD /d 24 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4D36E972-E325-11CE-BFC1-08002BE10318}\0002" /v "PnPCapabilities" /t REG_DWORD /d 24 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Class\{4D36E972-E325-11CE-BFC1-08002BE10318}\0003" /v "PnPCapabilities" /t REG_DWORD /d 24 /f >nul 2>&1

rem USB power management for WiFi dongles
reg add "HKLM\SYSTEM\CurrentControlSet\Services\USB" /v "DisableSelectiveSuspend" /t REG_DWORD /d 1 /f >nul 2>&1

echo.
echo ✓ Gestion energie reseau optimisee!
echo   Gain attendu: Stabilite reseau +30%%, latence -15%%
echo.
pause
GOTO Menu

:QoSGaming
CLS
title QoS Gaming Ultra

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo CONFIGURATION QOS GAMING ULTRA
echo.
echo Cette optimisation va:
echo • Prioriser le trafic gaming
echo • Configurer les classes de service
echo • Optimiser la gestion des paquets
echo • Reduire la gigue reseau
echo.

choice /c:yn /n /m "Continuer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Configuration QoS Gaming...

rem Gaming QoS optimizations
netsh int tcp set global rss=enabled >nul 2>&1
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global ecncapability=enabled >nul 2>&1

rem Priority settings for gaming traffic
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Affinity" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Background Only" /t REG_SZ /d "False" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" /t REG_DWORD /d 6 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Scheduling Category" /t REG_SZ /d "High" /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "SFIO Priority" /t REG_SZ /d "High" /f >nul 2>&1

echo.
echo ✓ QoS Gaming configure!
echo   Gain attendu: Priorite trafic gaming, -25%% gigue
echo.
pause
GOTO Menu

:OptimisationComplete
CLS
title Optimisation Complete Reseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION COMPLETE RESEAU
echo.
echo Cette option applique TOUTES les optimisations reseau:
echo • TCP/IP Stack Ultra
echo • DNS Ultra Rapide (Cloudflare)
echo • Desactivation Throttling
echo • Gestion Energie Optimisee
echo • QoS Gaming Ultra
echo.
echo ATTENTION: Un redemarrage sera necessaire.
echo.

choice /c:yn /n /m "Appliquer toutes les optimisations reseau? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo ========================================
echo APPLICATION DE TOUTES LES OPTIMISATIONS
echo ========================================

echo.
echo [1/5] TCP/IP Stack Ultra...
call :TCPIPStackSilent

echo [2/5] DNS Ultra Rapide...
call :DNSUltraSilent

echo [3/5] Desactivation Throttling...
call :DesactiverThrottlingSilent

echo [4/5] Gestion Energie...
call :GestionEnergieSilent

echo [5/5] QoS Gaming...
call :QoSGamingSilent

echo.
echo ========================================
echo OPTIMISATION COMPLETE TERMINEE!
echo ========================================
echo.
echo Gains attendus:
echo • Latence reseau: -70%%
echo • Debit: +40%%
echo • Stabilite: +50%%
echo • Priorite gaming: Maximale
echo.
echo REDEMARRAGE RECOMMANDE pour appliquer tous les changements.
echo.
choice /c:yn /n /m "Redemarrer maintenant? [Y]es/[N]o: "
if %ERRORLEVEL% == 1 shutdown /r /t 10 /c "Redemarrage pour optimisations reseau - MZEER Edition"

GOTO Menu

:TestLatence
CLS
title Test de Latence

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo TEST DE LATENCE RESEAU
echo.
echo Test en cours vers differents serveurs...
echo.

echo Test Google DNS (*******):
ping -n 4 *******

echo.
echo Test Cloudflare DNS (*******):
ping -n 4 *******

echo.
echo Test serveur de jeu (exemple):
ping -n 4 google.com

echo.
pause
GOTO Menu

:Informations
CLS
title Informations Reseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo INFORMATIONS DETAILLEES - OPTIMISATIONS RESEAU
echo.
echo TCP/IP STACK ULTRA:
echo • Optimise la pile TCP/IP Windows pour reduire la latence
echo • Configure les buffers et parametres de congestion
echo • Active les fonctionnalites TCP avancees (RSS, Chimney, etc.)
echo • Gain: -30%% latence, +20%% debit
echo.
echo DNS ULTRA RAPIDE:
echo • Configure Cloudflare DNS (*******) - Le plus rapide au monde
echo • Optimise le cache DNS Windows
echo • Reduit le temps de resolution des noms
echo • Gain: -50%% temps DNS, navigation +25%% plus rapide
echo.
echo DESACTIVATION THROTTLING:
echo • Supprime les limitations Windows sur la bande passante
echo • Desactive QoS automatique
echo • Maximise l'utilisation de la connexion
echo • Gain: +25%% debit, -40%% latence
echo.
echo GESTION ENERGIE RESEAU:
echo • Desactive l'economie d'energie des cartes reseau
echo • Empeche la mise en veille des adaptateurs
echo • Assure des performances constantes
echo • Gain: +30%% stabilite, -15%% latence
echo.
echo QOS GAMING:
echo • Priorise le trafic des jeux
echo • Configure les classes de service Windows
echo • Reduit la gigue et les micro-coupures
echo • Gain: Priorite maximale gaming, -25%% gigue
echo.
pause
GOTO Menu

:Restaurer
CLS
title Restaurer Parametres Reseau

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo RESTAURATION PARAMETRES RESEAU PAR DEFAUT
echo.
echo ATTENTION: Cette action va restaurer tous les parametres
echo reseau aux valeurs par defaut de Windows.
echo.
echo Cela annulera toutes les optimisations appliquees.
echo.

choice /c:yn /n /m "Etes-vous sur de vouloir restaurer? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Restauration en cours...

rem Reset TCP/IP stack
netsh int tcp reset >nul 2>&1
netsh int ip reset >nul 2>&1
netsh winsock reset >nul 2>&1

rem Reset DNS to automatic
netsh interface ip set dns "Ethernet" dhcp >nul 2>&1
netsh interface ip set dns "Wi-Fi" dhcp >nul 2>&1

rem Flush DNS cache
ipconfig /flushdns >nul 2>&1

rem Reset network throttling to default
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" /t REG_DWORD /d 10 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "SystemResponsiveness" /t REG_DWORD /d 20 /f >nul 2>&1

echo.
echo ✓ Parametres reseau restaures aux valeurs par defaut.
echo   Un redemarrage est recommande.
echo.
pause
GOTO Menu

rem Silent functions for complete optimization
:TCPIPStackSilent
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TcpAckFrequency" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v "TCPNoDelay" /t REG_DWORD /d 1 /f >nul 2>&1
exit /b

:DNSUltraSilent
netsh interface ip set dns "Ethernet" static ******* >nul 2>&1
netsh interface ip set dns "Wi-Fi" static ******* >nul 2>&1
ipconfig /flushdns >nul 2>&1
exit /b

:DesactiverThrottlingSilent
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" /t REG_DWORD /d 4294967295 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\Psched" /v "NonBestEffortLimit" /t REG_DWORD /d 0 /f >nul 2>&1
exit /b

:GestionEnergieSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Services\USB" /v "DisableSelectiveSuspend" /t REG_DWORD /d 1 /f >nul 2>&1
exit /b

:QoSGamingSilent
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" /t REG_DWORD /d 6 /f >nul 2>&1
reg add "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Scheduling Category" /t REG_SZ /d "High" /f >nul 2>&1
exit /b

:Retour
exit
