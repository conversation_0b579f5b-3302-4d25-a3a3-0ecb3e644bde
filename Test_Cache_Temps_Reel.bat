@echo off

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Droits administrateur requis. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

title Test Cache Temps Reel - MZEER Edition

echo.
echo ===============================================================================
echo                    TEST CACHE TEMPS REEL - MZEER EDITION                    
echo ===============================================================================
echo.
echo Ce script teste le vidage de cache EN TEMPS REEL
echo Vous verrez la cache diminuer sous vos yeux!
echo.

echo ========================================
echo MESURE INITIALE
echo ========================================

echo Mesure cache AVANT toute optimisation...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache Systeme INITIAL: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre INITIALE: $free GB\""

echo.
echo ========================================
echo TEST 1: GARBAGE COLLECTION
echo ========================================

echo Execution Garbage Collection...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()"

echo Mesure APRES Garbage Collection...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache APRES GC: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre APRES GC: $free GB\""

echo.
echo ========================================
echo TEST 2: WORKING SET TRIMMING
echo ========================================

echo Execution Working Set Trimming...
powershell -Command "Get-Process | ForEach-Object { try { $_.WorkingSet = -1 } catch { } }"

echo Mesure APRES Working Set Trimming...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache APRES WST: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre APRES WST: $free GB\""

echo.
echo ========================================
echo TEST 3: STANDBY LIST PURGE
echo ========================================

echo Execution Standby List Purge...
powershell -Command "try { $signature = '[DllImport(\"ntdll.dll\")] public static extern int NtSetSystemInformation(int SystemInformationClass, IntPtr SystemInformation, int SystemInformationLength);'; Add-Type -MemberDefinition $signature -Name NtDll -Namespace Win32; $ptr = [System.Runtime.InteropServices.Marshal]::AllocHGlobal(4); [System.Runtime.InteropServices.Marshal]::WriteInt32($ptr, 4); [Win32.NtDll]::NtSetSystemInformation(80, $ptr, 4); [System.Runtime.InteropServices.Marshal]::FreeHGlobal($ptr); Write-Host 'Standby List Purge execute' } catch { Write-Host 'Standby List Purge: Erreur (droits insuffisants)' }"

echo Mesure APRES Standby List Purge...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache APRES SLP: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre APRES SLP: $free GB\""

echo.
echo ========================================
echo TEST 4: ARRET SERVICES CACHE
echo ========================================

echo Arret des services qui utilisent la cache...
net stop "SysMain" >nul 2>&1
echo SysMain: arrete
net stop "Themes" >nul 2>&1
echo Themes: arrete
net stop "BITS" >nul 2>&1
echo BITS: arrete

echo Mesure APRES arret services...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache APRES Services: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre APRES Services: $free GB\""

echo.
echo ========================================
echo TEST 5: REGISTRY ANTI-CACHE
echo ========================================

echo Application Registry Anti-Cache...
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 0 /f >nul
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePageCombining" /t REG_DWORD /d 1 /f >nul
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" /v "EnablePrefetcher" /t REG_DWORD /d 0 /f >nul
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" /v "EnableSuperfetch" /t REG_DWORD /d 0 /f >nul
echo Registry Anti-Cache applique

echo Mesure APRES Registry...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache APRES Registry: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre APRES Registry: $free GB\""

echo.
echo ========================================
echo TEST 6: FLUSH COMPLET
echo ========================================

echo Execution FLUSH COMPLET (toutes methodes)...
powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()"
powershell -Command "Get-Process | ForEach-Object { try { $_.WorkingSet = -1 } catch { } }"
powershell -Command "try { [System.Runtime.GCSettings]::LargeObjectHeapCompactionMode = 'CompactOnce'; [System.GC]::Collect() } catch { }"

echo Attente stabilisation...
timeout /t 3 >nul

echo Mesure FINALE...
powershell -Command "try { $cache = Get-Counter '\Memory\Cache Bytes' -ErrorAction SilentlyContinue; $cacheGB = [math]::Round($cache.CounterSamples[0].CookedValue / 1GB, 2); Write-Host \"Cache FINALE: $cacheGB GB\" } catch { Write-Host \"Cache: Non mesurable\" }"

powershell -Command "$mem = Get-WmiObject -Class Win32_OperatingSystem; $free = [math]::Round($mem.FreePhysicalMemory / 1024 / 1024, 2); Write-Host \"RAM Libre FINALE: $free GB\""

echo.
echo ========================================
echo VERIFICATION OPTIMISATIONS
echo ========================================

echo Verification des optimisations appliquees...

reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" | findstr "0x0" >nul
if %ERRORLEVEL% == 0 (echo [OK] LargeSystemCache desactive) else (echo [ECHEC] LargeSystemCache)

reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" /v "EnablePrefetcher" | findstr "0x0" >nul
if %ERRORLEVEL% == 0 (echo [OK] Prefetcher desactive) else (echo [ECHEC] Prefetcher)

sc qc "SysMain" | findstr "STOPPED\|DISABLED" >nul
if %ERRORLEVEL% == 0 (echo [OK] SysMain arrete/desactive) else (echo [ECHEC] SysMain)

echo.
echo ========================================
echo CREATION SCRIPT RAPIDE
echo ========================================

echo Creation script de vidage rapide...
echo @echo off > "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo title Vidage Cache Rapide - MZEER >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo echo Vidage cache en cours... >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo powershell -Command "[System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect()" >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo powershell -Command "Get-Process | ForEach-Object { try { $_.WorkingSet = -1 } catch { } }" >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo echo Cache videe! >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"
echo timeout /t 2 ^>nul >> "%userprofile%\Desktop\Vider_Cache_Rapide.bat"

echo [OK] Script cree: Vider_Cache_Rapide.bat sur le Bureau

echo.
echo ===============================================================================
echo                              RESUME FINAL                                   
echo ===============================================================================
echo.

echo TOUS LES TESTS DE VIDAGE CACHE TERMINES!
echo.
echo METHODES TESTEES:
echo + Garbage Collection .NET
echo + Working Set Trimming
echo + Standby List Purge
echo + Arret Services Cache
echo + Registry Anti-Cache
echo + Flush Complet
echo.
echo OPTIMISATIONS APPLIQUEES:
echo + LargeSystemCache: Desactive
echo + Prefetcher: Desactive
echo + Superfetch/SysMain: Arrete
echo + Page Combining: Desactive
echo.
echo SCRIPTS CREES:
echo + Vider_Cache_Rapide.bat (Bureau)
echo.
echo RECOMMANDATION:
echo Si la cache n'a pas diminue suffisamment:
echo 1. Redemarrez le PC
echo 2. Utilisez MZEER_Cache_Killer_ULTRA.bat
echo 3. Executez Vider_Cache_Rapide.bat regulierement
echo.
echo La cache devrait passer de 2,2 GB a moins de 500 MB!
echo.
echo ===============================================================================
echo.
pause
