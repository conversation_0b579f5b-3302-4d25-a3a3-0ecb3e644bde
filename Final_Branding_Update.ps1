# Script final pour remplacer toutes les références MathysM par MZEER
Write-Host "🔄 Mise à jour finale du branding..." -ForegroundColor Yellow

# Fonction pour traiter un fichier
function Update-File {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        try {
            $content = Get-Content -Path $FilePath -Raw -Encoding UTF8
            $originalContent = $content
            
            # Remplacements
            $content = $content -replace "MathysM", "MZEER"
            $content = $content -replace "beacons\.ai/mathysm", "https://www.twitch.tv/mzeer_"
            $content = $content -replace "beacons\.ai\/mathysm", "https://www.twitch.tv/mzeer_"
            
            # Si le contenu a changé, sauvegarder
            if ($content -ne $originalContent) {
                Set-Content -Path $FilePath -Value $content -Encoding UTF8
                Write-Host "  ✅ $FilePath" -ForegroundColor Green
                return $true
            }
        } catch {
            Write-Host "  ❌ Erreur: $FilePath" -ForegroundColor Red
        }
    }
    return $false
}

# Liste des fichiers à traiter
$files = @(
    "Opti Détaillés\Optimisations Avancées\Mémoire Avancée\Menu Mémoire.bat",
    "Opti Détaillés\Optimisations Avancées\CPU Avancé\Menu CPU.bat",
    "Opti Détaillés\Optimisations Avancées\Réseau & Latence\Menu Réseau.bat"
)

$updatedCount = 0

foreach ($file in $files) {
    if (Update-File -FilePath $file) {
        $updatedCount++
    }
}

Write-Host "`n📊 Résultats:" -ForegroundColor Yellow
Write-Host "• Fichiers mis à jour: $updatedCount" -ForegroundColor White
Write-Host "✅ Branding MZEER appliqué!" -ForegroundColor Green
