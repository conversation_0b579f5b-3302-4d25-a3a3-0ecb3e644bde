@echo off
chcp 65001
set "base_path=%~dp0..\..\..\"
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations Avancées - Menu Principal

echo Create by MathysM - beacons.ai/mathysm

echo.
echo.
echo        █████╗ ██╗   ██╗ █████╗ ███╗   ██╗ ██████╗███████╗███████╗███████╗
echo       ██╔══██╗██║   ██║██╔══██╗████╗  ██║██╔════╝██╔════╝██╔════╝██╔════╝
echo       ███████║██║   ██║███████║██╔██╗ ██║██║     █████╗  █████╗  ███████╗
echo       ██╔══██║╚██╗ ██╔╝██╔══██║██║╚██╗██║██║     ██╔══╝  ██╔══╝  ╚════██║
echo       ██║  ██║ ╚████╔╝ ██║  ██║██║ ╚████║╚██████╗███████╗███████╗███████║
echo       ╚═╝  ╚═╝  ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝╚══════╝╚══════╝
echo.
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          OPTIMISATIONS AVANCÉES                             ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. 🌐 Réseau & Latence          │  7. 🚀 Gaming Ultra Performance        ║
echo ║  2. 🧠 Mémoire Avancée           │  8. 🛡️  Sécurité vs Performance        ║
echo ║  3. ⚡ CPU Avancé                │  9. 🎯 Optimisation Complète           ║
echo ║  4. 🎮 Gaming Avancé             │  A. 🔄 Restaurer Tout                  ║
echo ║  5. 💾 Stockage Avancé           │  B. ℹ️  Informations                   ║
echo ║  6. 🖥️  Display & Visual         │  0. 🚪 Retour Menu Principal           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890AB /M "Sélectionnez votre choix:"
IF ERRORLEVEL 12 GOTO Informations
IF ERRORLEVEL 11 GOTO RestaurerTout
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO OptimisationComplete
IF ERRORLEVEL 8 GOTO Securite
IF ERRORLEVEL 7 GOTO GamingUltra
IF ERRORLEVEL 6 GOTO Display
IF ERRORLEVEL 5 GOTO Stockage
IF ERRORLEVEL 4 GOTO Gaming
IF ERRORLEVEL 3 GOTO CPU
IF ERRORLEVEL 2 GOTO Memoire
IF ERRORLEVEL 1 GOTO Reseau

:Reseau
start "" "%~dp0\Réseau & Latence\Menu Réseau.bat"
GOTO Menu

:Memoire
start "" "%~dp0\Mémoire Avancée\Menu Mémoire.bat"
GOTO Menu

:CPU
start "" "%~dp0\CPU Avancé\Menu CPU.bat"
GOTO Menu

:Gaming
start "" "%~dp0\Gaming Avancé\Menu Gaming.bat"
GOTO Menu

:Stockage
start "" "%~dp0\Stockage Avancé\Menu Stockage.bat"
GOTO Menu

:Display
start "" "%~dp0\Display & Visual\Menu Display.bat"
GOTO Menu

:GamingUltra
start "" "%~dp0\Gaming Ultra Performance\Menu Gaming Ultra.bat"
GOTO Menu

:Securite
start "" "%~dp0\Sécurité vs Performance\Menu Sécurité.bat"
GOTO Menu

:OptimisationComplete
CLS
title Optimisation Complète - Confirmation

echo Create by MathysM - beacons.ai/mathysm
echo.
echo.
echo ⚠️  ATTENTION - OPTIMISATION COMPLÈTE ⚠️
echo.
echo Cette option appliquera TOUTES les optimisations avancées.
echo Cela peut prendre plusieurs minutes et nécessite un redémarrage.
echo.
echo Un point de restauration sera créé automatiquement.
echo.
choice /c:yn /n /m "Êtes-vous sûr de vouloir continuer? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ExecuterOptimisationComplete
if %ERRORLEVEL% == 2 GOTO Menu

:ExecuterOptimisationComplete
call "%~dp0\Scripts\Optimisation Complète.bat"
GOTO Menu

:RestaurerTout
CLS
title Restauration Complète - Confirmation

echo Create by MathysM - beacons.ai/mathysm
echo.
echo.
echo ⚠️  ATTENTION - RESTAURATION COMPLÈTE ⚠️
echo.
echo Cette option restaurera TOUTES les optimisations avancées.
echo Vos paramètres reviendront aux valeurs par défaut de Windows.
echo.
choice /c:yn /n /m "Êtes-vous sûr de vouloir continuer? [Y]es/[N]o"
if %ERRORLEVEL% == 1 GOTO ExecuterRestaurationComplete
if %ERRORLEVEL% == 2 GOTO Menu

:ExecuterRestaurationComplete
call "%~dp0\Scripts\Restauration Complète.bat"
GOTO Menu

:Informations
CLS
title Informations - Optimisations Avancées

echo Create by MathysM - beacons.ai/mathysm
echo.
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            INFORMATIONS DÉTAILLÉES                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌐 RÉSEAU & LATENCE:
echo    • Optimisation TCP/IP Stack pour réduire la latence
echo    • Configuration DNS rapide (Cloudflare, Google)
echo    • Désactivation du throttling réseau Windows
echo    • Optimisation de la gestion d'énergie des cartes réseau
echo.
echo 🧠 MÉMOIRE AVANCÉE:
echo    • Optimisation de la compression mémoire
echo    • Configuration intelligente du fichier d'échange
echo    • Gestion avancée de la liste de veille mémoire
echo    • Activation du support des grandes pages
echo.
echo ⚡ CPU AVANCÉ:
echo    • Optimisation du planificateur de processus
echo    • Gestion intelligente du parking des cœurs
echo    • Configuration des classes de priorité
echo    • Optimisation de l'affinité des interruptions
echo.
echo 🎮 GAMING AVANCÉ:
echo    • Optimisations avancées du planificateur GPU
echo    • Optimisations DirectX 11/12 spécifiques
echo    • Gestion avancée du mode plein écran
echo    • Suppression complète de Game DVR
echo.
echo 💾 STOCKAGE AVANCÉ:
echo    • Optimisations NTFS avancées
echo    • Planification intelligente de la défragmentation
echo    • Optimisation du cache d'écriture
echo    • Configuration avancée Prefetch/Superfetch
echo.
echo 🛡️  SÉCURITÉ vs PERFORMANCE:
echo    • Exclusions intelligentes Windows Defender
echo    • Ajustement de la protection temps réel
echo    • Optimisation SmartScreen
echo    • Configuration optimale UAC
echo.
echo Appuyez sur une touche pour revenir au menu...
pause >nul
GOTO Menu

:Retour
exit

:Quitter
exit
