@echo off
CLS

rem Vérifie si le script est exécuté en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script nécessite des droits administratifs. Redémarrage avec élévation de privilèges...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations Avancées - Menu Principal

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo        ██████╗ ██████╗ ████████╗██╗███╗   ███╗██╗███████╗ █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
echo       ██╔═══██╗██╔══██╗╚══██╔══╝██║████╗ ████║██║██╔════╝██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
echo       ██║   ██║██████╔╝   ██║   ██║██╔████╔██║██║███████╗███████║   ██║   ██║██║   ██║██╔██╗ ██║
echo       ██║   ██║██╔═══╝    ██║   ██║██║╚██╔╝██║██║╚════██║██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
echo       ╚██████╔╝██║        ██║   ██║██║ ╚═╝ ██║██║███████║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
echo        ╚═════╝ ╚═╝        ╚═╝   ╚═╝╚═╝     ╚═╝╚═╝╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝
echo.
echo                                    AVANCÉES
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          OPTIMISATIONS AVANCÉES                             ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  1. Réseau et Latence            │  5. Gaming Ultra Performance           ║
echo ║  2. Mémoire Avancée              │  6. Optimisation Complète              ║
echo ║  3. CPU Avancé                   │  7. Informations                       ║
echo ║  4. Gaming Avancé                │  0. Retour Menu Principal              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

CHOICE /C 1234567890 /M "Sélectionnez votre choix:"
IF ERRORLEVEL 8 GOTO Retour
IF ERRORLEVEL 7 GOTO Informations
IF ERRORLEVEL 6 GOTO OptimisationComplete
IF ERRORLEVEL 5 GOTO GamingUltra
IF ERRORLEVEL 4 GOTO Gaming
IF ERRORLEVEL 3 GOTO CPU
IF ERRORLEVEL 2 GOTO Memoire
IF ERRORLEVEL 1 GOTO Reseau

:Reseau
start "" "%~dp0\Réseau & Latence\Menu Réseau.bat"
GOTO Menu

:Memoire
start "" "%~dp0\Mémoire Avancée\Menu Mémoire.bat"
GOTO Menu

:CPU
start "" "%~dp0\CPU Avancé\Menu CPU.bat"
GOTO Menu

:Gaming
echo Gaming Avancé - En développement
pause
GOTO Menu

:GamingUltra
start "" "%~dp0\Gaming Ultra Performance\Menu Gaming Ultra.bat"
GOTO Menu

:OptimisationComplete
CLS
title Optimisation Complète

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo ATTENTION - OPTIMISATION COMPLÈTE
echo.
echo Cette option appliquera TOUTES les optimisations avancées disponibles.
echo Cela inclut les optimisations réseau, mémoire, CPU et gaming.
echo.
echo Un redémarrage sera nécessaire après l'optimisation.
echo.
choice /c:yn /n /m "Voulez-vous continuer? [Y]es/[N]o"
if %ERRORLEVEL% == 1 start "" "%~dp0\Scripts\Optimisation Complète.bat"
GOTO Menu

:Informations
CLS
title Informations

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo.
echo INFORMATIONS SUR LES OPTIMISATIONS AVANCÉES
echo.
echo RÉSEAU et LATENCE:
echo    • Optimisation TCP/IP Stack pour réduire la latence
echo    • Configuration DNS rapide (Cloudflare, Google)
echo    • Désactivation du throttling réseau Windows
echo    • Optimisation de la gestion d'énergie des cartes réseau
echo.
echo MÉMOIRE AVANCÉE:
echo    • Optimisation de la compression mémoire
echo    • Configuration intelligente du fichier d'échange
echo    • Gestion avancée de la liste de veille mémoire
echo    • Activation du support des grandes pages
echo.
echo CPU AVANCÉ:
echo    • Optimisation du planificateur de processus
echo    • Gestion intelligente du parking des cœurs
echo    • Configuration des classes de priorité
echo    • Optimisation de l'affinité des interruptions
echo.
echo GAMING ULTRA PERFORMANCE:
echo    • MSI Mode et optimisation des interruptions
echo    • Timer Resolution Ultra (0.5ms)
echo    • GPU Ultra Optimization
echo    • Memory Ultra Tuning
echo    • CPU Micro-Optimizations
echo    • Network Gaming Extreme
echo    • Storage Gaming Ultra
echo    • Display Ultra Sync
echo.
echo Gains attendus:
echo    • FPS: +40% à +80%
echo    • Input Lag: -80% à -95%
echo    • Latence Réseau: -70%
echo    • Temps de Chargement: -60%
echo.
echo Appuyez sur une touche pour revenir au menu...
pause >nul
GOTO Menu

:Retour
exit
