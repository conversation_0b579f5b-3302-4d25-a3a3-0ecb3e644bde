@echo off
CLS

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script necessite des droits administratifs. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

title Verification des Optimisations - MZEER Edition

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo ===============================================================================
echo                        VERIFICATION DES OPTIMISATIONS                       
echo                               MZEER EDITION                                    
echo ===============================================================================
echo.

echo Verification en cours des optimisations appliquees...
echo.

echo ========================================
echo [1/4] VERIFICATION RESEAU
echo ========================================

echo.
echo [RESEAU] Verification TCP/IP Stack...
netsh int tcp show global | findstr /i "autotuninglevel chimney rss"

echo.
echo [RESEAU] Verification DNS...
nslookup google.com | findstr /i "*******"
if %ERRORLEVEL% == 0 (
    echo [OK] DNS Cloudflare ******* configure
) else (
    echo [INFO] DNS par defaut ou autre configuration
)

echo.
echo [RESEAU] Verification Network Throttling...
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295"
if %ERRORLEVEL% == 0 (
    echo [OK] Network Throttling desactive
) else (
    echo [ATTENTION] Network Throttling peut etre actif
)

echo.
echo [RESEAU] Verification QoS Gaming...
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Priority" 2>nul | findstr /i "0x6"
if %ERRORLEVEL% == 0 (
    echo [OK] QoS Gaming priorite configuree
) else (
    echo [INFO] QoS Gaming priorite par defaut
)

echo.
echo ========================================
echo [2/4] VERIFICATION MEMOIRE
echo ========================================

echo.
echo [MEMOIRE] Verification Memory Compression...
powershell -Command "Get-MMAgent | Select-Object MemoryCompression"

echo.
echo [MEMOIRE] Verification Paging File...
powershell -Command "Get-WmiObject -Class Win32_PageFileSetting | Select-Object Name, InitialSize, MaximumSize"

echo.
echo [MEMOIRE] Verification DisablePagingExecutive...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Paging Executive desactive
) else (
    echo [INFO] Paging Executive par defaut
)

echo.
echo [MEMOIRE] Verification Large Page Support...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargePageMinimum" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] Large Page Support active
) else (
    echo [INFO] Large Page Support par defaut
)

echo.
echo ========================================
echo [3/4] VERIFICATION CPU
echo ========================================

echo.
echo [CPU] Verification CPU Scheduling...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26"
if %ERRORLEVEL% == 0 (
    echo [OK] CPU Scheduling optimise (38 decimal)
) else (
    echo [INFO] CPU Scheduling par defaut
)

echo.
echo [CPU] Verification Core Parking...
powercfg /query SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 0cc5b647-c1df-4637-891a-dec35c318583 | findstr /i "0x00000000"
if %ERRORLEVEL% == 0 (
    echo [OK] Core Parking desactive (Gaming)
) else (
    echo [INFO] Core Parking actif
)

echo.
echo [CPU] Verification Gaming Priority...
reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile\Tasks\Games" /v "Scheduling Category" 2>nul | findstr /i "High"
if %ERRORLEVEL% == 0 (
    echo [OK] Gaming Priority configuree sur High
) else (
    echo [INFO] Gaming Priority par defaut
)

echo.
echo [CPU] Verification CPU Power Management...
powercfg /query SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 bc5038f7-23e0-4960-96da-33abaf5935ec | findstr /i "0x00000064"
if %ERRORLEVEL% == 0 (
    echo [OK] CPU Power Management Gaming Ultra (100%%)
) else (
    echo [INFO] CPU Power Management par defaut
)

echo.
echo ========================================
echo [4/4] VERIFICATION GAMING
echo ========================================

echo.
echo [GAMING] Verification Timer Resolution...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Timer Resolution Ultra active
) else (
    echo [INFO] Timer Resolution par defaut
)

echo.
echo [GAMING] Verification Game Mode...
reg query "HKCU\Software\Microsoft\GameBar" /v "AutoGameModeEnabled" 2>nul | findstr /i "0x1"
if %ERRORLEVEL% == 0 (
    echo [OK] Game Mode Ultra active
) else (
    echo [INFO] Game Mode par defaut
)

echo.
echo [GAMING] Verification Game DVR...
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] Game DVR desactive
) else (
    echo [ATTENTION] Game DVR peut etre actif
)

echo.
echo [GAMING] Verification Hardware GPU Scheduling...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2"
if %ERRORLEVEL% == 0 (
    echo [OK] Hardware GPU Scheduling active
) else (
    echo [INFO] Hardware GPU Scheduling par defaut
)

echo.
echo [GAMING] Verification GPU TDR...
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" 2>nul | findstr /i "0x0"
if %ERRORLEVEL% == 0 (
    echo [OK] GPU TDR desactive
) else (
    echo [INFO] GPU TDR par defaut
)

echo.
echo [GAMING] Verification Fullscreen Optimizations...
reg query "HKCU\System\GameConfigStore" /v "GameDVR_FSEBehaviorMode" 2>nul | findstr /i "0x2"
if %ERRORLEVEL% == 0 (
    echo [OK] Fullscreen Optimizations configurees
) else (
    echo [INFO] Fullscreen Optimizations par defaut
)

echo.
echo ========================================
echo VERIFICATION TERMINEE
echo ========================================
echo.

echo RESUME DES OPTIMISATIONS DETECTEES:
echo.

rem Compteur des optimisations actives
set /a count=0

reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" /v "NetworkThrottlingIndex" 2>nul | findstr /i "4294967295" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" 2>nul | findstr /i "0x1" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\PriorityControl" /v "Win32PrioritySeparation" 2>nul | findstr /i "0x26" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" /v "GlobalTimerResolutionRequests" 2>nul | findstr /i "0x1" >nul && set /a count+=1
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\GameDVR" /v "GameDVR_Enabled" 2>nul | findstr /i "0x0" >nul && set /a count+=1
reg query "HKLM\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "HwSchMode" 2>nul | findstr /i "0x2" >nul && set /a count+=1

echo Optimisations detectees: %count%/6 optimisations principales
echo.

if %count% GEQ 5 (
    echo [EXCELLENT] Votre systeme est TRES BIEN optimise!
    echo Vous devriez constater des ameliorations significatives.
) else if %count% GEQ 3 (
    echo [BON] Votre systeme est correctement optimise.
    echo Certaines optimisations peuvent necessiter un redemarrage.
) else (
    echo [ATTENTION] Peu d'optimisations detectees.
    echo Un redemarrage peut etre necessaire pour activer les changements.
)

echo.
echo INFORMATIONS SYSTEME ACTUELLES:
echo.

echo [PERFORMANCE] Utilisation CPU actuelle:
powershell -Command "Get-WmiObject -Class Win32_Processor | Select-Object LoadPercentage | Format-Table -HideTableHeaders"

echo [PERFORMANCE] Utilisation memoire:
powershell -Command "$os = Get-WmiObject -Class Win32_OperatingSystem; $totalRAM = [math]::Round($os.TotalVisibleMemorySize/1MB,2); $freeRAM = [math]::Round($os.FreePhysicalMemory/1MB,2); $usedRAM = $totalRAM - $freeRAM; $usagePercent = [math]::Round(($usedRAM/$totalRAM)*100,1); Write-Host \"RAM Utilisee: $usedRAM GB / $totalRAM GB ($usagePercent%%)\""

echo.
echo [PERFORMANCE] Test de latence reseau:
ping -n 1 ******* | findstr /i "temps"

echo.
echo ========================================
echo VERIFICATION COMPLETE
echo ========================================
echo.
echo Pour de meilleures performances, assurez-vous que:
echo 1. Le systeme a ete redémarre apres les optimisations
echo 2. Aucun antivirus tiers n'interfere avec les parametres
echo 3. Windows Update est a jour
echo 4. Les pilotes GPU sont a jour
echo.
echo Merci d'avoir utilise MZEER Edition!
echo.
pause
