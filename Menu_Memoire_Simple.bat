@echo off
CLS

rem Verifie si le script est execute en tant qu'administrateur
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Le script necessite des droits administratifs. Redemarrage...
    powershell.exe -Command "Start-Process '%~0' -Verb RunAs"
    exit /b
)

:Menu
CLS

title Optimisations Memoire Avancee - MZEER Edition

echo Create by MZEER - https://www.twitch.tv/mzeer_

echo.
echo.
echo ===============================================================================
echo                        OPTIMISATIONS MEMOIRE AVANCEE                        
echo                               MZEER EDITION                                    
echo ===============================================================================
echo.

echo -------------------------------------------------------------------------------
echo                          MENU MEMOIRE AVANCE                               
echo -------------------------------------------------------------------------------
echo   1. Compression Memoire          6. Analyse Memoire                      
echo   2. Fichier d'Echange Gaming     7. Optimisation Complete               
echo   3. Liste de Veille Memoire      8. Nettoyage Memoire                   
echo   4. Support Grandes Pages        9. Restaurer Memoire                   
echo   5. Gestion Memoire Avancee      0. Retour Menu Principal               
echo -------------------------------------------------------------------------------
echo.

CHOICE /C 1234567890 /M "Selectionnez votre choix:"
IF ERRORLEVEL 10 GOTO Retour
IF ERRORLEVEL 9 GOTO RestaurerMemoire
IF ERRORLEVEL 8 GOTO NettoyageMemoire
IF ERRORLEVEL 7 GOTO OptimisationComplete
IF ERRORLEVEL 6 GOTO AnalyseMemoire
IF ERRORLEVEL 5 GOTO GestionAvancee
IF ERRORLEVEL 4 GOTO GrandesPages
IF ERRORLEVEL 3 GOTO ListeVeille
IF ERRORLEVEL 2 GOTO FichierEchange
IF ERRORLEVEL 1 GOTO CompressionMemoire

:CompressionMemoire
CLS
title Compression Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION COMPRESSION MEMOIRE
echo.
echo Cette optimisation va:
echo • Optimiser la compression memoire Windows
echo • Reduire l'utilisation de la memoire physique
echo • Ameliorer les performances systeme
echo • Configurer les parametres de compression avances
echo.

choice /c:yn /n /m "Optimiser la compression memoire? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Optimisation de la compression memoire...

rem Enable and optimize memory compression
powershell -Command "try { Enable-MMAgent -MemoryCompression -ErrorAction SilentlyContinue } catch { }" >nul 2>&1
powershell -Command "try { Set-MMAgent -MemoryCompression $true -ErrorAction SilentlyContinue } catch { }" >nul 2>&1

rem Registry optimizations for memory compression
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 0 /f >nul 2>&1

echo.
echo ✓ Compression memoire optimisee!
echo   Gain attendu: -15%% utilisation RAM, +10%% performances
echo.
pause
GOTO Menu

:FichierEchange
CLS
title Fichier d'Echange Gaming

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo CONFIGURATION FICHIER D'ECHANGE GAMING
echo.
echo Choisissez votre configuration:
echo.
echo 1. Gaming Ultra (Taille fixe optimisee)
echo 2. Configuration Equilibree
echo 3. Desactiver le fichier d'echange
echo 4. Retour au menu
echo.

CHOICE /C 1234 /M "Selectionnez votre configuration:"
IF ERRORLEVEL 4 GOTO Menu
IF ERRORLEVEL 3 GOTO DesactiverPaging
IF ERRORLEVEL 2 GOTO PagingEquilibre
IF ERRORLEVEL 1 GOTO PagingGaming

:PagingGaming
echo.
echo Configuration fichier d'echange Gaming...
rem Set fixed paging file size for gaming (2GB initial, 4GB max)
powershell -Command "try { $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $false; $cs.Put(); $pf = Get-WmiObject -Class Win32_PageFileSetting; if($pf) { $pf.Delete() }; Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{name='C:\pagefile.sys'; InitialSize=2048; MaximumSize=4096} } catch { Write-Host 'Erreur configuration paging file' }" >nul 2>&1
echo ✓ Fichier d'echange Gaming configure (2-4GB)!
GOTO PagingComplete

:PagingEquilibre
echo.
echo Configuration fichier d'echange equilibre...
rem Set system managed paging file
powershell -Command "try { $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $true; $cs.Put() } catch { Write-Host 'Erreur configuration paging file' }" >nul 2>&1
echo ✓ Fichier d'echange equilibre configure!
GOTO PagingComplete

:DesactiverPaging
echo.
echo ATTENTION: Desactiver le fichier d'echange peut causer des problemes
echo si vous n'avez pas suffisamment de RAM (16GB+ recommandes).
echo.
choice /c:yn /n /m "Etes-vous sur de vouloir desactiver? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Desactivation du fichier d'echange...
powershell -Command "try { $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $false; $cs.Put(); $pf = Get-WmiObject -Class Win32_PageFileSetting; if($pf) { $pf.Delete() } } catch { Write-Host 'Erreur desactivation paging file' }" >nul 2>&1
echo ✓ Fichier d'echange desactive!
GOTO PagingComplete

:PagingComplete
echo.
echo REDEMARRAGE OBLIGATOIRE pour appliquer les changements.
echo.
pause
GOTO Menu

:ListeVeille
CLS
title Liste de Veille Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION LISTE DE VEILLE MEMOIRE
echo.
echo Cette optimisation va:
echo • Optimiser la gestion de la liste de veille memoire
echo • Liberer automatiquement la memoire inutilisee
echo • Ameliorer la reactivite du systeme
echo • Configurer le nettoyage automatique
echo.

choice /c:yn /n /m "Optimiser la liste de veille memoire? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Optimisation de la liste de veille memoire...

rem Memory standby list optimizations
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "ClearPageFileAtShutdown" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 0 /f >nul 2>&1

rem Clear standby memory
powershell -Command "try { [System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect() } catch { }" >nul 2>&1

echo.
echo ✓ Liste de veille memoire optimisee!
echo   Gain attendu: +20%% reactivite systeme
echo.
pause
GOTO Menu

:GrandesPages
CLS
title Support Grandes Pages

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo ACTIVATION SUPPORT GRANDES PAGES
echo.
echo Cette optimisation va:
echo • Activer le support des grandes pages (Large Pages)
echo • Ameliorer les performances des applications compatibles
echo • Optimiser l'utilisation memoire pour les jeux
echo • Configurer les privileges necessaires
echo.

choice /c:yn /n /m "Activer le support des grandes pages? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Activation du support des grandes pages...

rem Enable large pages support
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargePageMinimum" /t REG_DWORD /d 0 /f >nul 2>&1

rem Grant lock pages in memory privilege
powershell -Command "try { $user = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name; secedit /export /cfg temp.cfg /quiet; (Get-Content temp.cfg) -replace 'SeLockMemoryPrivilege = ', ('SeLockMemoryPrivilege = ' + $user + ',') | Set-Content temp.cfg; secedit /configure /db temp.sdb /cfg temp.cfg /quiet; Remove-Item temp.cfg, temp.sdb -Force } catch { }" >nul 2>&1

echo.
echo ✓ Support des grandes pages active!
echo   REDEMARRAGE RECOMMANDE pour activation complete.
echo.
pause
GOTO Menu

:GestionAvancee
CLS
title Gestion Memoire Avancee

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo GESTION MEMOIRE AVANCEE
echo.
echo Cette optimisation va:
echo • Optimiser la gestion avancee de la memoire
echo • Configurer les parametres de cache systeme
echo • Ameliorer l'allocation memoire
echo • Optimiser les performances des applications
echo.

choice /c:yn /n /m "Appliquer la gestion memoire avancee? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Application de la gestion memoire avancee...

rem Advanced memory management settings
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "SystemPages" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "SecondLevelDataCache" /t REG_DWORD /d 1024 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "ThirdLevelDataCache" /t REG_DWORD /d 8192 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePageCombining" /t REG_DWORD /d 0 /f >nul 2>&1

echo.
echo ✓ Gestion memoire avancee appliquee!
echo   Gain attendu: +15%% performances applications
echo.
pause
GOTO Menu

:AnalyseMemoire
CLS
title Analyse Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo ANALYSE DE L'UTILISATION MEMOIRE
echo.
echo Analyse en cours...
echo.

powershell -Command "Get-WmiObject -Class Win32_OperatingSystem | Select-Object @{Name='RAM Totale (GB)';Expression={[math]::Round($_.TotalVisibleMemorySize/1MB,2)}}, @{Name='RAM Libre (GB)';Expression={[math]::Round($_.FreePhysicalMemory/1MB,2)}}, @{Name='RAM Utilisee (GB)';Expression={[math]::Round(($_.TotalVisibleMemorySize-$_.FreePhysicalMemory)/1MB,2)}}, @{Name='Utilisation (%)';Expression={[math]::Round((($_.TotalVisibleMemorySize-$_.FreePhysicalMemory)/$_.TotalVisibleMemorySize)*100,1)}} | Format-List"

echo.
echo Processus utilisant le plus de memoire:
powershell -Command "Get-Process | Sort-Object WorkingSet -Descending | Select-Object -First 10 Name, @{Name='Memoire (MB)';Expression={[math]::Round($_.WorkingSet/1MB,2)}} | Format-Table -AutoSize"

echo.
pause
GOTO Menu

:OptimisationComplete
CLS
title Optimisation Complete Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo OPTIMISATION COMPLETE MEMOIRE
echo.
echo Cette option applique TOUTES les optimisations memoire:
echo • Compression Memoire
echo • Fichier d'Echange Gaming
echo • Liste de Veille Optimisee
echo • Support Grandes Pages
echo • Gestion Memoire Avancee
echo.

choice /c:yn /n /m "Appliquer toutes les optimisations memoire? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo ========================================
echo APPLICATION DE TOUTES LES OPTIMISATIONS
echo ========================================

echo.
echo [1/5] Compression Memoire...
call :CompressionSilent

echo [2/5] Fichier d'Echange Gaming...
call :PagingGamingSilent

echo [3/5] Liste de Veille...
call :ListeVeilleSilent

echo [4/5] Support Grandes Pages...
call :GrandesPagesSilent

echo [5/5] Gestion Avancee...
call :GestionAvanceeSilent

echo.
echo ========================================
echo OPTIMISATION COMPLETE TERMINEE!
echo ========================================
echo.
echo Gains attendus:
echo • Utilisation RAM: -20%%
echo • Performances: +25%%
echo • Reactivite: +30%%
echo • Stabilite: +15%%
echo.
echo REDEMARRAGE OBLIGATOIRE pour appliquer tous les changements.
echo.
choice /c:yn /n /m "Redemarrer maintenant? [Y]es/[N]o: "
if %ERRORLEVEL% == 1 shutdown /r /t 10 /c "Redemarrage pour optimisations memoire - MZEER Edition"

GOTO Menu

:NettoyageMemoire
CLS
title Nettoyage Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo NETTOYAGE MEMOIRE EN COURS
echo.

echo • Vidage de la liste de veille memoire...
powershell -Command "try { [System.GC]::Collect(); [System.GC]::WaitForPendingFinalizers(); [System.GC]::Collect() } catch { }" >nul 2>&1

echo • Nettoyage du cache systeme...
powershell -Command "try { Clear-RecycleBin -Force -ErrorAction SilentlyContinue } catch { }" >nul 2>&1

echo • Liberation de la memoire inutilisee...
powershell -Command "try { [System.Runtime.GCSettings]::LargeObjectHeapCompactionMode = 'CompactOnce'; [System.GC]::Collect() } catch { }" >nul 2>&1

echo.
echo ✓ Nettoyage memoire termine!
echo.
pause
GOTO Menu

:RestaurerMemoire
CLS
title Restaurer Memoire

echo Create by MZEER - https://www.twitch.tv/mzeer_
echo.
echo RESTAURATION PARAMETRES MEMOIRE
echo.
echo Cette action va restaurer tous les parametres memoire par defaut.
echo.

choice /c:yn /n /m "Restaurer les parametres memoire? [Y]es/[N]o: "
if %ERRORLEVEL% == 2 GOTO Menu

echo.
echo Restauration en cours...

rem Restore default memory settings
powershell -Command "try { Set-MMAgent -MemoryCompression $false -ErrorAction SilentlyContinue } catch { }" >nul 2>&1
powershell -Command "try { $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $true; $cs.Put() } catch { }" >nul 2>&1

rem Reset registry values
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" /t REG_DWORD /d 0 /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargeSystemCache" /t REG_DWORD /d 1 /f >nul 2>&1

echo.
echo ✓ Parametres memoire restaures par defaut.
echo   Un redemarrage est recommande.
echo.
pause
GOTO Menu

rem Silent functions
:CompressionSilent
powershell -Command "try { Enable-MMAgent -MemoryCompression -ErrorAction SilentlyContinue; Set-MMAgent -MemoryCompression $true -ErrorAction SilentlyContinue } catch { }" >nul 2>&1
exit /b

:PagingGamingSilent
powershell -Command "try { $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges; $cs.AutomaticManagedPagefile = $false; $cs.Put(); $pf = Get-WmiObject -Class Win32_PageFileSetting; if($pf) { $pf.Delete() }; Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{name='C:\pagefile.sys'; InitialSize=2048; MaximumSize=4096} } catch { }" >nul 2>&1
exit /b

:ListeVeilleSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "DisablePagingExecutive" /t REG_DWORD /d 1 /f >nul 2>&1
powershell -Command "try { [System.GC]::Collect() } catch { }" >nul 2>&1
exit /b

:GrandesPagesSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "LargePageMinimum" /t REG_DWORD /d 0 /f >nul 2>&1
exit /b

:GestionAvanceeSilent
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" /v "SystemPages" /t REG_DWORD /d 0 /f >nul 2>&1
exit /b

:Retour
exit
